#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeMonoBoldOblique9pt7bBitmaps[] PROGMEM = {
    0x39, 0xCC, 0x67, 0x31, 0x8C, 0x07, 0x38, 0x6C, 0xD9, 0x36, 0x48, 0x80,
    0x09, 0x0D, 0x86, 0xCF, 0xF7, 0xF9, 0xB3, 0xFD, 0xFE, 0x6C, 0x36, 0x1B,
    0x00, 0x00, 0x06, 0x07, 0x07, 0xE6, 0x33, 0x01, 0xE0, 0x7C, 0x06, 0x43,
    0x33, 0xBF, 0x83, 0x03, 0x00, 0x80, 0x1C, 0x11, 0x10, 0x88, 0x83, 0xB8,
    0xF3, 0xB8, 0x22, 0x21, 0x11, 0x07, 0x00, 0x0F, 0x1F, 0x30, 0x30, 0x38,
    0x7B, 0xDF, 0xCE, 0xFF, 0x7E, 0xFA, 0x80, 0x19, 0x8C, 0xC6, 0x63, 0x18,
    0xC6, 0x31, 0xC6, 0x30, 0x31, 0xC6, 0x31, 0x8C, 0x63, 0x31, 0x98, 0xCC,
    0x40, 0x08, 0x08, 0xFF, 0xFF, 0x38, 0x6C, 0x6C, 0x0C, 0x06, 0x03, 0x1F,
    0xFF, 0xF8, 0xC0, 0x60, 0x30, 0x10, 0x00, 0x36, 0x4C, 0x80, 0xFF, 0xFF,
    0xC0, 0xFC, 0x00, 0x00, 0x0C, 0x03, 0x00, 0xC0, 0x18, 0x06, 0x01, 0x80,
    0x30, 0x0C, 0x03, 0x00, 0x60, 0x18, 0x06, 0x00, 0xC0, 0x30, 0x00, 0x0F,
    0x0F, 0xCC, 0x6C, 0x36, 0x1B, 0x0D, 0x05, 0x86, 0xC3, 0x63, 0x3F, 0x8F,
    0x00, 0x06, 0x1C, 0x3C, 0x6C, 0x0C, 0x0C, 0x08, 0x18, 0x18, 0x18, 0xFE,
    0xFE, 0x07, 0x83, 0xF1, 0x8C, 0x43, 0x00, 0xC0, 0xE0, 0x70, 0x38, 0x38,
    0x1C, 0x6F, 0xF3, 0xFC, 0x1F, 0x1F, 0xC0, 0x60, 0x30, 0x30, 0x70, 0x38,
    0x06, 0x03, 0x03, 0xBF, 0x9F, 0x80, 0x03, 0x07, 0x0B, 0x1B, 0x32, 0x66,
    0xFF, 0xFF, 0x1E, 0x1E, 0x3F, 0x9F, 0x98, 0x0F, 0xC7, 0xF3, 0x18, 0x0C,
    0x06, 0x06, 0x7F, 0x1E, 0x00, 0x07, 0x87, 0xCE, 0x06, 0x06, 0x03, 0xF3,
    0xFD, 0xC6, 0xC3, 0x63, 0xBF, 0x8F, 0x80, 0xFF, 0xFF, 0xC3, 0x06, 0x06,
    0x0C, 0x18, 0x18, 0x30, 0x30, 0x60, 0x1F, 0x1F, 0xDC, 0x6C, 0x36, 0x31,
    0xF1, 0xF8, 0xC6, 0xC3, 0x63, 0xBF, 0x8F, 0x80, 0x1E, 0x3F, 0x33, 0x63,
    0x63, 0x67, 0x7F, 0x3E, 0x06, 0x1C, 0xF8, 0xF0, 0x77, 0x00, 0x00, 0xEE,
    0x1C, 0x70, 0x00, 0x00, 0x03, 0x0C, 0x61, 0x08, 0x00, 0x00, 0xC1, 0xE1,
    0xE1, 0xE0, 0xF0, 0x07, 0x00, 0xF0, 0x0C, 0x7F, 0xDF, 0xF0, 0x00, 0x00,
    0x7F, 0xFF, 0xF0, 0x30, 0x0F, 0x00, 0xE0, 0x1E, 0x07, 0xC7, 0x87, 0x83,
    0x00, 0x7D, 0xFF, 0x18, 0x30, 0xE3, 0x9C, 0x30, 0x01, 0xC3, 0x80, 0x0F,
    0x0F, 0xCC, 0x6C, 0x36, 0x72, 0x79, 0x7D, 0xB6, 0xDA, 0x6F, 0xB3, 0xD8,
    0x0C, 0x07, 0xE1, 0xE0, 0x0F, 0x83, 0xF0, 0x1E, 0x03, 0xC0, 0xD8, 0x31,
    0x87, 0xF1, 0xFE, 0x30, 0xDF, 0x3F, 0xC7, 0x80, 0x3F, 0xC7, 0xFC, 0x61,
    0x8C, 0x31, 0xFC, 0x3F, 0x84, 0x19, 0x83, 0x30, 0x6F, 0xFB, 0xFE, 0x00,
    0x0F, 0xF1, 0xFF, 0x30, 0x66, 0x06, 0x60, 0x0C, 0x00, 0xC0, 0x0C, 0x00,
    0xE0, 0xC7, 0xF8, 0x3F, 0x00, 0x3F, 0x87, 0xF8, 0x63, 0x8C, 0x31, 0x06,
    0x60, 0xCC, 0x19, 0x86, 0x31, 0xCF, 0xF3, 0xF8, 0x00, 0x3F, 0xE3, 0xFE,
    0x18, 0x61, 0xB6, 0x1F, 0x01, 0xF0, 0x32, 0x03, 0x00, 0x30, 0x4F, 0xFC,
    0xFF, 0xC0, 0x3F, 0xF3, 0xFE, 0x18, 0x61, 0xB6, 0x1F, 0x03, 0xF0, 0x32,
    0x03, 0x00, 0x30, 0x0F, 0xC0, 0xFC, 0x00, 0x0F, 0xE3, 0xFC, 0xC1, 0x30,
    0x06, 0x01, 0x80, 0x31, 0xF6, 0x3E, 0xE1, 0x9F, 0xF0, 0xF8, 0x00, 0x1E,
    0xF3, 0xCF, 0x18, 0x61, 0x84, 0x10, 0xC3, 0xFC, 0x3F, 0xC3, 0x08, 0x31,
    0x8F, 0xBC, 0xFB, 0xC0, 0x3F, 0xCF, 0xF0, 0x60, 0x10, 0x0C, 0x03, 0x00,
    0xC0, 0x20, 0x18, 0x3F, 0xCF, 0xF0, 0x07, 0xF0, 0x7F, 0x00, 0x80, 0x18,
    0x01, 0x80, 0x18, 0x61, 0x84, 0x10, 0xC3, 0x0F, 0xE0, 0x7C, 0x00, 0x3E,
    0xE7, 0xFC, 0x66, 0x0D, 0x81, 0x60, 0x7C, 0x0E, 0xC1, 0x98, 0x31, 0x1F,
    0x3B, 0xE7, 0x00, 0x3F, 0x07, 0xE0, 0x30, 0x06, 0x00, 0xC0, 0x10, 0x06,
    0x00, 0xC3, 0x18, 0x6F, 0xFB, 0xFF, 0x00, 0x38, 0x39, 0xC3, 0xC7, 0x3C,
    0x79, 0xE3, 0xDA, 0x1F, 0xF0, 0x9D, 0x8C, 0xCC, 0x60, 0x67, 0xCF, 0x3C,
    0x78, 0x3C, 0xF9, 0xE7, 0x87, 0x18, 0x3C, 0xC1, 0x66, 0x1B, 0xB0, 0xCD,
    0x06, 0x78, 0x31, 0xC3, 0xCE, 0x3E, 0x30, 0x0F, 0x0F, 0xE7, 0x1D, 0x83,
    0xC0, 0xF0, 0x3C, 0x0F, 0x06, 0xE3, 0x9F, 0xC3, 0xC0, 0x3F, 0xC7, 0xFC,
    0x61, 0x8C, 0x31, 0x8E, 0x3F, 0x87, 0xE1, 0x80, 0x30, 0x0F, 0xC3, 0xF0,
    0x00, 0x0F, 0x0F, 0xE7, 0x1D, 0x83, 0xC0, 0xF0, 0x3C, 0x0F, 0x06, 0xE3,
    0x1F, 0xC3, 0xC0, 0x80, 0x7F, 0x3F, 0xC0, 0x3F, 0xC3, 0xFE, 0x18, 0x61,
    0x86, 0x10, 0xE3, 0xFC, 0x3F, 0x83, 0x18, 0x31, 0xCF, 0x8F, 0xF8, 0x70,
    0x1E, 0xCF, 0xF7, 0x19, 0x80, 0x70, 0x1F, 0x81, 0xF3, 0x0C, 0xC3, 0x3F,
    0x8B, 0xC0, 0x7F, 0xCF, 0xF9, 0x93, 0x66, 0x60, 0xC0, 0x18, 0x02, 0x00,
    0xC0, 0x18, 0x0F, 0xC1, 0xF8, 0x00, 0xF9, 0xFF, 0x7D, 0x83, 0x30, 0x64,
    0x09, 0x83, 0x30, 0x66, 0x0C, 0xE3, 0x0F, 0xC0, 0xF0, 0x00, 0xF9, 0xFE,
    0x3D, 0x83, 0x30, 0xC6, 0x30, 0xE6, 0x0D, 0x81, 0xB0, 0x3C, 0x07, 0x00,
    0x60, 0x00, 0xF9, 0xFF, 0x3D, 0x83, 0x36, 0x64, 0xC8, 0xBF, 0x35, 0xE7,
    0xB8, 0xE7, 0x1C, 0xE3, 0x18, 0x00, 0x3C, 0xF3, 0xCF, 0x1C, 0xC0, 0xD8,
    0x0F, 0x00, 0x60, 0x0F, 0x01, 0xB8, 0x31, 0x8F, 0x3C, 0xF3, 0xC0, 0x79,
    0xEE, 0x38, 0xC6, 0x19, 0x81, 0xE0, 0x38, 0x06, 0x00, 0xC0, 0x18, 0x0F,
    0xC3, 0xF8, 0x00, 0x3F, 0xCF, 0xF3, 0x18, 0xCC, 0x06, 0x03, 0x01, 0x80,
    0xC6, 0x61, 0xBF, 0xCF, 0xF0, 0x1E, 0x3C, 0xC1, 0x83, 0x06, 0x08, 0x30,
    0x60, 0xC1, 0x06, 0x0F, 0x1E, 0x00, 0x06, 0x31, 0x86, 0x31, 0x8C, 0x31,
    0x8C, 0x61, 0x8C, 0x60, 0x1E, 0x78, 0x30, 0x60, 0xC1, 0x86, 0x0C, 0x18,
    0x30, 0x41, 0x8F, 0x1E, 0x00, 0x08, 0x1C, 0x3C, 0x76, 0xE7, 0xC3, 0x7F,
    0xFF, 0xFC, 0x88, 0x80, 0x0F, 0x07, 0xE1, 0xF9, 0xFE, 0xE3, 0x30, 0xCF,
    0xFD, 0xFF, 0x38, 0x07, 0x00, 0x60, 0x0F, 0xC1, 0xFC, 0x71, 0xCC, 0x19,
    0x83, 0x30, 0xDF, 0xFB, 0xBC, 0x00, 0x1F, 0xCF, 0xF6, 0x1B, 0x00, 0xC0,
    0x30, 0x0F, 0xF1, 0xF8, 0x01, 0xE0, 0x38, 0x03, 0x0F, 0x63, 0xFC, 0xC3,
    0x30, 0x66, 0x0C, 0xC3, 0x9F, 0xF9, 0xF7, 0x00, 0x1F, 0x1F, 0xD8, 0x3F,
    0xFF, 0xFE, 0x1B, 0xFC, 0xF8, 0x07, 0xC3, 0xF1, 0x81, 0xFE, 0x7F, 0x84,
    0x03, 0x00, 0xC0, 0x30, 0x3F, 0x8F, 0xE0, 0x1E, 0xE7, 0xFD, 0x86, 0x60,
    0xCC, 0x19, 0xC6, 0x3F, 0xC1, 0xD8, 0x03, 0x00, 0xE1, 0xF8, 0x3E, 0x00,
    0x38, 0x1E, 0x01, 0x00, 0xDC, 0x3F, 0x8C, 0x62, 0x19, 0x84, 0x63, 0x3D,
    0xFF, 0x7C, 0x06, 0x03, 0x00, 0x03, 0xC3, 0xE0, 0x20, 0x30, 0x18, 0x0C,
    0x3F, 0xFF, 0xE0, 0x01, 0x81, 0x80, 0x07, 0xF3, 0xF8, 0x0C, 0x04, 0x06,
    0x03, 0x01, 0x80, 0xC0, 0x40, 0x67, 0xE3, 0xE0, 0x38, 0x0E, 0x01, 0x80,
    0x4F, 0x37, 0xCF, 0x83, 0xC0, 0xF0, 0x26, 0x39, 0xEE, 0x78, 0x1F, 0x0F,
    0x01, 0x80, 0xC0, 0x60, 0x20, 0x30, 0x18, 0x0C, 0x3F, 0xFF, 0xE0, 0x7E,
    0xE7, 0xFF, 0x33, 0x32, 0x63, 0x66, 0x36, 0x62, 0xF7, 0x7F, 0x67, 0x77,
    0x8F, 0xF8, 0xC3, 0x10, 0x66, 0x08, 0xC3, 0x3C, 0x7F, 0x8F, 0x1F, 0x0F,
    0xE6, 0x1F, 0x03, 0xC0, 0xF8, 0x67, 0xF0, 0xF8, 0x3F, 0xE3, 0xFF, 0x1C,
    0x31, 0x83, 0x18, 0x31, 0x86, 0x3F, 0xE3, 0x78, 0x30, 0x03, 0x00, 0xFC,
    0x0F, 0x80, 0x1E, 0xEF, 0xFD, 0x86, 0x60, 0xCC, 0x19, 0xC7, 0x3F, 0xE1,
    0xE8, 0x03, 0x00, 0x60, 0x3E, 0x07, 0xC0, 0x39, 0xDF, 0xF1, 0xC0, 0x60,
    0x10, 0x0C, 0x0F, 0xF3, 0xF8, 0x1F, 0x7F, 0x63, 0x7E, 0x1F, 0xC3, 0xFE,
    0xFC, 0x10, 0x08, 0x0C, 0x1F, 0xEF, 0xF1, 0x80, 0x80, 0xC0, 0x60, 0x3F,
    0x8F, 0x80, 0xF3, 0xFC, 0xF6, 0x09, 0x86, 0x61, 0x98, 0xE7, 0xF8, 0xFE,
    0xFB, 0xFF, 0x7C, 0xC6, 0x19, 0x83, 0x60, 0x6C, 0x07, 0x00, 0xC0, 0xF1,
    0xFE, 0x3D, 0xB3, 0x37, 0xC7, 0xF8, 0xEE, 0x1D, 0xC3, 0x30, 0x79, 0xEF,
    0x38, 0xEE, 0x0F, 0x01, 0xE0, 0x6E, 0x3C, 0xE7, 0xBC, 0x3C, 0xF3, 0x8F,
    0x18, 0xC1, 0x9C, 0x19, 0x81, 0xF0, 0x0E, 0x00, 0xE0, 0x0C, 0x01, 0x80,
    0xFC, 0x0F, 0xC0, 0x7F, 0xBF, 0xD9, 0xC1, 0x83, 0x83, 0x1B, 0xFD, 0xFE,
    0x06, 0x1C, 0x60, 0xC1, 0x86, 0x3C, 0x70, 0x30, 0x41, 0x83, 0x07, 0x06,
    0x00, 0x33, 0x32, 0x26, 0x66, 0x44, 0xCC, 0xC8, 0x0C, 0x0E, 0x04, 0x0C,
    0x0C, 0x0C, 0x0F, 0x0F, 0x18, 0x18, 0x10, 0x30, 0xF0, 0xE0, 0x38, 0x7C,
    0xF7, 0xC1, 0xC0};

const GFXglyph FreeMonoBoldOblique9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 11, 0, 1},       // 0x20 ' '
    {0, 5, 11, 11, 4, -10},    // 0x21 '!'
    {7, 7, 5, 11, 4, -10},     // 0x22 '"'
    {12, 9, 12, 11, 2, -10},   // 0x23 '#'
    {26, 9, 14, 11, 2, -11},   // 0x24 '$'
    {42, 9, 11, 11, 2, -10},   // 0x25 '%'
    {55, 8, 10, 11, 2, -9},    // 0x26 '&'
    {65, 2, 5, 11, 6, -10},    // 0x27 '''
    {67, 5, 14, 11, 5, -10},   // 0x28 '('
    {76, 5, 14, 11, 2, -10},   // 0x29 ')'
    {85, 8, 7, 11, 3, -10},    // 0x2A '*'
    {92, 9, 9, 11, 2, -8},     // 0x2B '+'
    {103, 4, 5, 11, 2, -1},    // 0x2C ','
    {106, 9, 2, 11, 2, -5},    // 0x2D '-'
    {109, 3, 2, 11, 4, -1},    // 0x2E '.'
    {110, 11, 15, 11, 1, -12}, // 0x2F '/'
    {131, 9, 12, 11, 2, -11},  // 0x30 '0'
    {145, 8, 12, 11, 2, -11},  // 0x31 '1'
    {157, 10, 12, 11, 1, -11}, // 0x32 '2'
    {172, 9, 12, 11, 2, -11},  // 0x33 '3'
    {186, 8, 10, 11, 2, -9},   // 0x34 '4'
    {196, 9, 11, 11, 3, -10},  // 0x35 '5'
    {209, 9, 12, 11, 3, -11},  // 0x36 '6'
    {223, 8, 11, 11, 3, -10},  // 0x37 '7'
    {234, 9, 12, 11, 2, -11},  // 0x38 '8'
    {248, 8, 12, 11, 3, -11},  // 0x39 '9'
    {260, 4, 8, 11, 4, -7},    // 0x3A ':'
    {264, 6, 11, 11, 2, -7},   // 0x3B ';'
    {273, 10, 8, 11, 2, -8},   // 0x3C '<'
    {283, 10, 6, 11, 1, -7},   // 0x3D '='
    {291, 10, 8, 11, 1, -8},   // 0x3E '>'
    {301, 7, 11, 11, 4, -10},  // 0x3F '?'
    {311, 9, 15, 11, 2, -11},  // 0x40 '@'
    {328, 11, 11, 11, 0, -10}, // 0x41 'A'
    {344, 11, 11, 11, 0, -10}, // 0x42 'B'
    {360, 12, 11, 11, 1, -10}, // 0x43 'C'
    {377, 11, 11, 11, 0, -10}, // 0x44 'D'
    {393, 12, 11, 11, 0, -10}, // 0x45 'E'
    {410, 12, 11, 11, 0, -10}, // 0x46 'F'
    {427, 11, 11, 11, 1, -10}, // 0x47 'G'
    {443, 12, 11, 11, 0, -10}, // 0x48 'H'
    {460, 10, 11, 11, 1, -10}, // 0x49 'I'
    {474, 12, 11, 11, 0, -10}, // 0x4A 'J'
    {491, 11, 11, 11, 0, -10}, // 0x4B 'K'
    {507, 11, 11, 11, 0, -10}, // 0x4C 'L'
    {523, 13, 11, 11, 0, -10}, // 0x4D 'M'
    {541, 13, 11, 11, 0, -10}, // 0x4E 'N'
    {559, 10, 11, 11, 1, -10}, // 0x4F 'O'
    {573, 11, 11, 11, 0, -10}, // 0x50 'P'
    {589, 10, 14, 11, 1, -10}, // 0x51 'Q'
    {607, 12, 11, 11, 0, -10}, // 0x52 'R'
    {624, 10, 11, 11, 2, -10}, // 0x53 'S'
    {638, 11, 11, 11, 1, -10}, // 0x54 'T'
    {654, 11, 11, 11, 1, -10}, // 0x55 'U'
    {670, 11, 11, 11, 1, -10}, // 0x56 'V'
    {686, 11, 11, 11, 1, -10}, // 0x57 'W'
    {702, 12, 11, 11, 0, -10}, // 0x58 'X'
    {719, 11, 11, 11, 1, -10}, // 0x59 'Y'
    {735, 10, 11, 11, 1, -10}, // 0x5A 'Z'
    {749, 7, 14, 11, 4, -10},  // 0x5B '['
    {762, 5, 15, 11, 4, -12},  // 0x5C '\'
    {772, 7, 14, 11, 2, -10},  // 0x5D ']'
    {785, 8, 6, 11, 3, -11},   // 0x5E '^'
    {791, 11, 2, 11, -1, 3},   // 0x5F '_'
    {794, 3, 3, 11, 5, -11},   // 0x60 '`'
    {796, 10, 8, 11, 1, -7},   // 0x61 'a'
    {806, 11, 11, 11, 0, -10}, // 0x62 'b'
    {822, 10, 8, 11, 1, -7},   // 0x63 'c'
    {832, 11, 11, 11, 1, -10}, // 0x64 'd'
    {848, 9, 8, 11, 1, -7},    // 0x65 'e'
    {857, 10, 11, 11, 2, -10}, // 0x66 'f'
    {871, 11, 12, 11, 1, -7},  // 0x67 'g'
    {888, 10, 11, 11, 1, -10}, // 0x68 'h'
    {902, 9, 11, 11, 1, -10},  // 0x69 'i'
    {915, 9, 15, 11, 1, -10},  // 0x6A 'j'
    {932, 10, 11, 11, 1, -10}, // 0x6B 'k'
    {946, 9, 11, 11, 1, -10},  // 0x6C 'l'
    {959, 12, 8, 11, 0, -7},   // 0x6D 'm'
    {971, 11, 8, 11, 1, -7},   // 0x6E 'n'
    {982, 10, 8, 11, 1, -7},   // 0x6F 'o'
    {992, 12, 12, 11, -1, -7}, // 0x70 'p'
    {1010, 11, 12, 11, 1, -7}, // 0x71 'q'
    {1027, 10, 8, 11, 1, -7},  // 0x72 'r'
    {1037, 8, 8, 11, 2, -7},   // 0x73 's'
    {1045, 9, 11, 11, 1, -10}, // 0x74 't'
    {1058, 10, 8, 11, 1, -7},  // 0x75 'u'
    {1068, 11, 8, 11, 1, -7},  // 0x76 'v'
    {1079, 11, 8, 11, 1, -7},  // 0x77 'w'
    {1090, 11, 8, 11, 1, -7},  // 0x78 'x'
    {1101, 12, 12, 11, 0, -7}, // 0x79 'y'
    {1119, 9, 8, 11, 2, -7},   // 0x7A 'z'
    {1128, 7, 14, 11, 3, -10}, // 0x7B '{'
    {1141, 4, 14, 11, 4, -10}, // 0x7C '|'
    {1148, 8, 14, 11, 2, -10}, // 0x7D '}'
    {1162, 9, 4, 11, 2, -6}};  // 0x7E '~'

const GFXfont FreeMonoBoldOblique9pt7b PROGMEM = {
    (uint8_t *)FreeMonoBoldOblique9pt7bBitmaps,
    (GFXglyph *)FreeMonoBoldOblique9pt7bGlyphs, 0x20, 0x7E, 18};

// Approx. 1839 bytes
