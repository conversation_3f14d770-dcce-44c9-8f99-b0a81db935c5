; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32@6.11.0
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_speed = 921600
build_flags = 
    -DCORE_DEBUG_LEVEL=5
    -D WEBSOCKETS_NETWORK_TYPE=10
	-D TINY_GSM_MODEM_SIM7600
lib_deps =
    digitaldragon/SSLClient@^1.3.2
    vshymanskyy/StreamDebugger@^1.0.1
	vshymanskyy/TinyGSM@^0.12.0
    symlink://../../..
