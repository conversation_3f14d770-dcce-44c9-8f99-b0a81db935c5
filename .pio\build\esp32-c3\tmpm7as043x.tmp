-o .pio/build/esp32-c3/src/main.cpp.o -c -march=rv32imc -std=gnu++11 -fexceptions -fno-rtti -Os -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -MMD -DPLATFORMIO=60118 -DARDUINO_ESP32C3_DEV -DCORE_DEBUG_LEVEL=3 -DARDUINO_USB_CDC_ON_BOOT=1 -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER=\"v4.4.7-dirty\" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -DARDUINO_ARCH_ESP32 -DESP32 -DF_CPU=240000000L -DARDUINO=10812 -DARDUINO_VARIANT=\"esp32c3\" "-DARDUINO_BOARD=\"Espressif ESP32-C3-DevKitM-1\"" -DARDUINO_PARTITION_partitions -Isrc -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WebServer/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPIFFS/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/FS/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Preferences/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/DNSServer/src -I.pio/libdeps/esp32-c3/WebSockets/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFiClientSecure/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Ethernet/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src "-I.pio/libdeps/esp32-c3/Adafruit MPU6050" "-I.pio/libdeps/esp32-c3/Adafruit Unified Sensor" "-I.pio/libdeps/esp32-c3/Adafruit BusIO" -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src -I.pio/libdeps/esp32-c3/ArduinoJson/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/newlib/platform_include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/freertos/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/freertos/include/esp_additions/freertos -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/freertos/port/riscv/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/freertos/include/esp_additions -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include/soc -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/include/soc/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/port/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hw_support/port/esp32c3/private_include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/heap/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/log/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/lwip/include/apps -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/lwip/include/apps/sntp -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/lwip/lwip/src/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/lwip/port/esp32/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/lwip/port/esp32/include/arch -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/soc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/soc/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/soc/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/hal/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/hal/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/hal/platform_port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rom/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rom/include/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rom/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_system/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_system/port/soc -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_system/port/include/riscv -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_system/port/public_compat -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/riscv/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/driver/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/driver/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_pm/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_ringbuf/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/efuse/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/efuse/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/vfs/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_wifi/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_event/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_netif/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_eth/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/tcpip_adapter/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_phy/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_phy/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_ipc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/app_trace/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_timer/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/mbedtls/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/mbedtls/mbedtls/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/mbedtls/esp_crt_bundle/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/app_update/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/spi_flash/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bootloader_support/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/nvs_flash/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/pthread/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_gdbstub/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_gdbstub/riscv -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_gdbstub/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espcoredump/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espcoredump/include/port/riscv -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/wpa_supplicant/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/wpa_supplicant/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/wpa_supplicant/esp_supplicant/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/ieee802154/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/console -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/asio/asio/asio/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/asio/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/common/osi/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/include/esp32c3/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/common/api/include/api -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/common/btc/profile/esp/blufi/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/common/btc/profile/esp/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/host/bluedroid/api/include/api -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_core -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_core/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_core/storage -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/btc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/api/core/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/api/models/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/bt/esp_ble_mesh/api -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/cbor/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/unity/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/unity/unity/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/cmock/CMock/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/coap/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/coap/libcoap/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/nghttp/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/nghttp/nghttp2/lib/includes -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-tls -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-tls/esp-tls-crypto -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_adc_cal/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_hid/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/tcp_transport/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_http_client/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_http_server/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_https_ota/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_https_server/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_lcd/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_lcd/interface -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/protobuf-c/protobuf-c -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/protocomm/include/common -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/protocomm/include/security -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/protocomm/include/transports -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/mdns/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_local_ctrl/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/sdmmc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_serial_slave_link/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_websocket_client/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/expat/expat/expat/lib -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/expat/port/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/wear_levelling/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/fatfs/diskio -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/fatfs/vfs -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/fatfs/src -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/freemodbus/freemodbus/common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/idf_test/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/idf_test/include/esp32c3 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/jsmn/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/json/cJSON -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/libsodium/libsodium/src/libsodium/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/libsodium/port_include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/mqtt/esp-mqtt/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/openssl/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/spiffs/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/wifi_provisioning/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/rmaker_common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_diagnostics/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/rtc_store/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_insights/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/json_parser/upstream/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/json_parser/upstream -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/json_generator/upstream -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_schedule/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp_secure_cert_mgr/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_rainmaker/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/gpio_button/button/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/qrcode/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/ws2812_led -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp_littlefs/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/tool -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/typedef -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/image -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/math -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/nn -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/layer -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/detect -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp-dl/include/model_zoo -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp32-camera/driver/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/esp32-camera/conversions/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/dotprod/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/support/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/support/mem/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/hann/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/iir/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/fir/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/add/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/sub/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/mul/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/addc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/mulc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/add/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/fft/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/dct/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/conv/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/common/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/include/fb_gfx/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32c3/dio_qspi/include -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32 -IC:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/esp32c3 src/main.cpp
