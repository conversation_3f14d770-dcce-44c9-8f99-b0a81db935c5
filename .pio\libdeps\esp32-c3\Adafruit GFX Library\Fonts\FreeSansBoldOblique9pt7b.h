#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSansBoldOblique9pt7bBitmaps[] PROGMEM = {
    0x21, 0x8E, 0x73, 0x18, 0xC6, 0x21, 0x19, 0xCE, 0x00, 0xEF, 0xDF, 0xBE,
    0x68, 0x80, 0x06, 0xC1, 0x99, 0xFF, 0xBF, 0xF1, 0xB0, 0x66, 0x0C, 0xC7,
    0xFC, 0xFF, 0x8C, 0x83, 0x30, 0x64, 0x00, 0x02, 0x00, 0xF0, 0x7F, 0x1D,
    0x73, 0xEE, 0x78, 0x0F, 0x00, 0xF8, 0x0F, 0xC1, 0xBB, 0xA7, 0x74, 0xEF,
    0xF8, 0xFE, 0x04, 0x00, 0x80, 0x3C, 0x11, 0xF8, 0x8E, 0x66, 0x31, 0x90,
    0xCE, 0x83, 0xF4, 0x07, 0xB0, 0x00, 0x9E, 0x04, 0xFC, 0x26, 0x31, 0x98,
    0xC4, 0x7E, 0x20, 0xF0, 0x07, 0x80, 0xFC, 0x1D, 0xC1, 0xDC, 0x1F, 0x80,
    0xE0, 0x3E, 0x37, 0x77, 0xE3, 0xEE, 0x3C, 0xE3, 0xCF, 0xFE, 0x3C, 0xE0,
    0xFF, 0xE8, 0x06, 0x06, 0x0C, 0x18, 0x38, 0x30, 0x70, 0x60, 0xE0, 0xE0,
    0xE0, 0xE0, 0xE0, 0xE0, 0x60, 0x70, 0x30, 0x0C, 0x0E, 0x06, 0x07, 0x07,
    0x07, 0x07, 0x07, 0x07, 0x06, 0x0E, 0x0C, 0x1C, 0x18, 0x30, 0x60, 0x60,
    0x32, 0xBF, 0x9C, 0xD2, 0x40, 0x0C, 0x06, 0x07, 0x1F, 0xFF, 0xF0, 0xC0,
    0xE0, 0x60, 0x77, 0x72, 0x6C, 0xFF, 0xC0, 0xFC, 0x02, 0x02, 0x04, 0x04,
    0x08, 0x08, 0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x80, 0x0F, 0x07, 0xE3,
    0x9D, 0xC7, 0x71, 0xDC, 0x7E, 0x1F, 0x8E, 0xE3, 0xB8, 0xEE, 0x73, 0xF8,
    0x3C, 0x00, 0x04, 0x3B, 0xF7, 0xE1, 0xC3, 0x06, 0x1C, 0x38, 0x70, 0xC1,
    0x87, 0x00, 0x0F, 0x87, 0xFC, 0xE3, 0xB8, 0x70, 0x0E, 0x03, 0x80, 0xF0,
    0x38, 0x1E, 0x07, 0x01, 0xC0, 0x7F, 0xCF, 0xF8, 0x0F, 0xC7, 0xFC, 0xE3,
    0xB8, 0x70, 0x1C, 0x0F, 0x03, 0xF0, 0x0E, 0x01, 0xDC, 0x3B, 0x8E, 0x7F,
    0x83, 0xE0, 0x03, 0xC0, 0xE0, 0x58, 0x2E, 0x13, 0x8C, 0xE6, 0x33, 0xFE,
    0xFF, 0x81, 0xC0, 0x60, 0x18, 0x0F, 0xE3, 0xFC, 0x60, 0x0C, 0x03, 0x78,
    0x7F, 0x9C, 0x70, 0x0E, 0x01, 0xDC, 0x33, 0x8E, 0x7F, 0x83, 0xE0, 0x0F,
    0x07, 0xE3, 0x9D, 0xC0, 0x7F, 0x1F, 0xEF, 0x3B, 0x8E, 0xE3, 0xB8, 0xCE,
    0x71, 0xF8, 0x3C, 0x00, 0x7F, 0xDF, 0xF0, 0x18, 0x0C, 0x06, 0x03, 0x81,
    0xC0, 0x60, 0x38, 0x0C, 0x07, 0x01, 0x80, 0x60, 0x00, 0x0F, 0x83, 0xFC,
    0xE3, 0x9C, 0x73, 0x9C, 0x3F, 0x0F, 0xE3, 0x8E, 0xE1, 0xDC, 0x3B, 0x8E,
    0x7F, 0xC3, 0xE0, 0x0F, 0x83, 0xF8, 0xE3, 0xB8, 0x77, 0x0E, 0xE1, 0xDC,
    0x7B, 0xFE, 0x3D, 0xC0, 0x33, 0x8E, 0x7F, 0x87, 0xC0, 0x77, 0x00, 0x00,
    0x0E, 0xE0, 0x39, 0xC0, 0x00, 0x01, 0xCE, 0x71, 0x19, 0x80, 0x00, 0x00,
    0x70, 0xFD, 0xF8, 0x70, 0x3F, 0x03, 0xF8, 0x1E, 0x01, 0x80, 0x7F, 0xDF,
    0xF0, 0x00, 0x00, 0xFF, 0xBF, 0xE0, 0x60, 0x1E, 0x07, 0xF0, 0x3F, 0x03,
    0x87, 0xEF, 0xC3, 0x80, 0x00, 0x00, 0x1F, 0x1F, 0xFE, 0x1F, 0x87, 0x01,
    0xC0, 0xE0, 0x70, 0x78, 0x3C, 0x0E, 0x00, 0x00, 0xE0, 0x38, 0x00, 0x00,
    0xFC, 0x00, 0xFF, 0xC0, 0xF0, 0x78, 0x70, 0x07, 0x38, 0x01, 0xCC, 0x3F,
    0x36, 0x31, 0x8D, 0x98, 0x63, 0xC4, 0x11, 0xF3, 0x0C, 0x6C, 0xC6, 0x73,
    0x3E, 0xF8, 0xE7, 0x3C, 0x1E, 0x00, 0x03, 0xFE, 0x00, 0x3F, 0x00, 0x01,
    0xE0, 0x0F, 0x00, 0xF8, 0x07, 0xC0, 0x6F, 0x03, 0x38, 0x31, 0xC3, 0x8E,
    0x1F, 0xF1, 0xFF, 0x8C, 0x1E, 0xE0, 0x76, 0x03, 0x80, 0x1F, 0xF0, 0xFF,
    0xC6, 0x0E, 0x70, 0x73, 0x87, 0x1F, 0xF0, 0xFF, 0x86, 0x0E, 0x70, 0x73,
    0x83, 0x9C, 0x38, 0xFF, 0xC7, 0xF8, 0x00, 0x07, 0xE0, 0xFF, 0x8F, 0x1E,
    0x70, 0x77, 0x00, 0x30, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x03, 0xBC,
    0x38, 0xFF, 0x83, 0xF0, 0x00, 0x1F, 0xE0, 0xFF, 0x86, 0x1E, 0x70, 0x73,
    0x83, 0x9C, 0x1C, 0xC0, 0xE6, 0x07, 0x70, 0x73, 0x83, 0x9C, 0x38, 0xFF,
    0x8F, 0xF0, 0x00, 0x1F, 0xF8, 0xFF, 0x86, 0x00, 0x70, 0x03, 0x80, 0x1F,
    0xF0, 0xFF, 0x86, 0x00, 0x70, 0x03, 0x80, 0x1C, 0x00, 0xFF, 0xC7, 0xFC,
    0x00, 0x1F, 0xF1, 0xFF, 0x18, 0x03, 0x80, 0x38, 0x03, 0xFC, 0x3F, 0xC7,
    0x00, 0x70, 0x07, 0x00, 0x70, 0x06, 0x00, 0xE0, 0x00, 0x07, 0xC1, 0xFE,
    0x38, 0x77, 0x03, 0x70, 0x0E, 0x00, 0xE1, 0xEE, 0x1E, 0xE0, 0x6E, 0x0E,
    0x70, 0xE7, 0xFC, 0x1F, 0x40, 0x1C, 0x1C, 0x60, 0x63, 0x83, 0x8E, 0x0E,
    0x38, 0x38, 0xFF, 0xC3, 0xFF, 0x1C, 0x1C, 0x70, 0x71, 0xC1, 0xC6, 0x06,
    0x18, 0x38, 0xE0, 0xE0, 0x39, 0xCE, 0x63, 0x39, 0xCE, 0x63, 0x39, 0xCE,
    0x00, 0x00, 0xC0, 0x18, 0x07, 0x00, 0xE0, 0x1C, 0x03, 0x00, 0xE0, 0x1C,
    0xE3, 0x9C, 0x73, 0x9C, 0x7F, 0x87, 0xC0, 0x1C, 0x3C, 0x71, 0xC1, 0x8E,
    0x0E, 0x70, 0x3B, 0x80, 0xFC, 0x03, 0xF0, 0x0E, 0xE0, 0x73, 0x81, 0xC7,
    0x07, 0x1C, 0x18, 0x38, 0xE0, 0xF0, 0x1C, 0x07, 0x01, 0x80, 0xE0, 0x38,
    0x0E, 0x03, 0x80, 0xC0, 0x70, 0x1C, 0x07, 0x01, 0xFF, 0x7F, 0x80, 0x1E,
    0x1F, 0x1E, 0x1E, 0x3E, 0x1E, 0x3E, 0x3E, 0x36, 0x3E, 0x36, 0x6E, 0x36,
    0x6C, 0x76, 0xCC, 0x76, 0xDC, 0x67, 0x9C, 0x67, 0x98, 0xE7, 0x18, 0xE7,
    0x18, 0x1C, 0x1C, 0x70, 0x63, 0xE1, 0x8F, 0x8E, 0x3E, 0x38, 0xDC, 0xC3,
    0x33, 0x1C, 0xEC, 0x71, 0xF1, 0xC7, 0xC6, 0x1E, 0x18, 0x38, 0xE0, 0xE0,
    0x07, 0xC0, 0xFF, 0x8E, 0x1E, 0xE0, 0x77, 0x03, 0xF0, 0x1F, 0x80, 0xFC,
    0x07, 0xE0, 0x77, 0x03, 0xBC, 0x38, 0xFF, 0x81, 0xF0, 0x00, 0x1F, 0xF0,
    0xFF, 0xC6, 0x0E, 0x70, 0x73, 0x83, 0x9C, 0x38, 0xFF, 0x87, 0xF8, 0x70,
    0x03, 0x80, 0x1C, 0x00, 0xC0, 0x0E, 0x00, 0x00, 0x07, 0xC0, 0xFF, 0x8F,
    0x1C, 0xE0, 0x77, 0x03, 0xB0, 0x1F, 0x80, 0xFC, 0x06, 0xE1, 0x77, 0x1F,
    0x3C, 0x78, 0xFF, 0xC1, 0xF6, 0x00, 0x20, 0x1F, 0xF0, 0xFF, 0xC6, 0x0E,
    0x70, 0x73, 0x83, 0x9C, 0x38, 0xFF, 0x87, 0xFC, 0x70, 0x73, 0x83, 0x9C,
    0x38, 0xC1, 0xC6, 0x0F, 0x00, 0x07, 0xE0, 0xFF, 0xC7, 0x0E, 0x70, 0x73,
    0x80, 0x1F, 0x80, 0x7F, 0x80, 0x7E, 0x00, 0x77, 0x03, 0xBC, 0x38, 0xFF,
    0xC3, 0xF8, 0x00, 0xFF, 0xDF, 0xF8, 0x70, 0x0E, 0x01, 0xC0, 0x38, 0x06,
    0x01, 0xC0, 0x38, 0x07, 0x00, 0xC0, 0x18, 0x07, 0x00, 0x38, 0x31, 0xC1,
    0x8C, 0x1C, 0xE0, 0xE7, 0x07, 0x38, 0x31, 0xC3, 0x9C, 0x1C, 0xE0, 0xE7,
    0x06, 0x38, 0x70, 0xFF, 0x03, 0xE0, 0x00, 0xE0, 0xFC, 0x1D, 0x87, 0x30,
    0xC6, 0x38, 0xC6, 0x19, 0xC3, 0xB0, 0x7E, 0x0F, 0x80, 0xF0, 0x1C, 0x03,
    0x00, 0xE1, 0xC3, 0xF1, 0xE3, 0xB8, 0xF1, 0xDC, 0x78, 0xCE, 0x6C, 0xE7,
    0x36, 0x63, 0xB3, 0x70, 0xD9, 0xB0, 0x7C, 0xD8, 0x3C, 0x78, 0x1E, 0x3C,
    0x0E, 0x1C, 0x07, 0x0E, 0x00, 0x0E, 0x1C, 0x38, 0xE0, 0xE7, 0x01, 0xD8,
    0x07, 0xE0, 0x0F, 0x00, 0x38, 0x01, 0xE0, 0x0F, 0xC0, 0x77, 0x01, 0x8E,
    0x0E, 0x38, 0x70, 0xF0, 0xE0, 0xEE, 0x39, 0xC7, 0x39, 0xC3, 0x70, 0x7C,
    0x0F, 0x80, 0xE0, 0x1C, 0x03, 0x00, 0xE0, 0x1C, 0x03, 0x80, 0x3F, 0xF3,
    0xFF, 0x00, 0xE0, 0x1C, 0x03, 0x80, 0x70, 0x0E, 0x01, 0xC0, 0x3C, 0x07,
    0x80, 0x70, 0x0F, 0xFC, 0xFF, 0xC0, 0x0F, 0x0F, 0x0C, 0x1C, 0x18, 0x18,
    0x18, 0x18, 0x30, 0x30, 0x30, 0x30, 0x60, 0x60, 0x60, 0x78, 0x78, 0x12,
    0x4C, 0x92, 0x49, 0x26, 0xD9, 0x20, 0x1E, 0x1E, 0x06, 0x06, 0x06, 0x0C,
    0x0C, 0x0C, 0x0C, 0x18, 0x18, 0x18, 0x18, 0x38, 0x30, 0xF0, 0xF0, 0x06,
    0x0E, 0x0E, 0x1B, 0x33, 0x33, 0x63, 0x63, 0xFF, 0xE0, 0xCC, 0x1F, 0x8F,
    0xF3, 0x1C, 0x06, 0x1F, 0x9F, 0xEE, 0x3B, 0x9C, 0xFF, 0x1D, 0xC0, 0x18,
    0x03, 0x00, 0xE0, 0x1D, 0xC3, 0xFC, 0x71, 0xDC, 0x3B, 0x87, 0x70, 0xEE,
    0x39, 0xCF, 0x7F, 0xCF, 0xE0, 0x0F, 0x0F, 0xF7, 0x1D, 0xC0, 0xE0, 0x38,
    0x0E, 0x03, 0x8E, 0x7F, 0x0F, 0x80, 0x00, 0x60, 0x06, 0x00, 0x61, 0xEE,
    0x3F, 0xE7, 0x9C, 0x71, 0xCE, 0x1C, 0xE1, 0xCE, 0x1C, 0xE3, 0x87, 0xF8,
    0x7F, 0x80, 0x1F, 0x0F, 0xE7, 0x1D, 0xC7, 0xFF, 0xFF, 0xFE, 0x03, 0x8E,
    0x7F, 0x0F, 0x80, 0x1C, 0xF3, 0x3F, 0xFD, 0xC7, 0x18, 0x63, 0x8E, 0x30,
    0xC0, 0x0F, 0x71, 0xFE, 0x3C, 0xE3, 0x8E, 0x70, 0xE7, 0x0E, 0x70, 0xC7,
    0x1C, 0x3F, 0xC3, 0xFC, 0x01, 0xCE, 0x38, 0x7F, 0x03, 0xE0, 0x18, 0x03,
    0x00, 0xE0, 0x1D, 0xE3, 0xFE, 0x71, 0xCC, 0x3B, 0x86, 0x70, 0xCC, 0x39,
    0x87, 0x30, 0xEE, 0x18, 0x39, 0xC0, 0x63, 0x39, 0xCE, 0x63, 0x39, 0xCE,
    0x00, 0x06, 0x06, 0x00, 0x0E, 0x0E, 0x0C, 0x0C, 0x1C, 0x1C, 0x1C, 0x18,
    0x18, 0x38, 0x38, 0x30, 0x70, 0xE0, 0x18, 0x03, 0x00, 0xE0, 0x1C, 0xE3,
    0x38, 0x6E, 0x1F, 0x83, 0xF0, 0x7E, 0x0E, 0xE1, 0x9C, 0x73, 0x8E, 0x38,
    0x39, 0xCE, 0x63, 0x39, 0xCE, 0x63, 0x39, 0xCE, 0x00, 0x3B, 0x9E, 0x3F,
    0xFF, 0x39, 0xC7, 0x71, 0xC6, 0x71, 0x86, 0x71, 0x8E, 0x63, 0x8E, 0x63,
    0x8C, 0xE3, 0x8C, 0xE3, 0x1C, 0x3B, 0xC7, 0xFC, 0xE3, 0xB8, 0x77, 0x0C,
    0xE1, 0x98, 0x73, 0x0E, 0xE1, 0xDC, 0x30, 0x0F, 0x87, 0xF9, 0xE7, 0xB8,
    0x7E, 0x0F, 0xC1, 0xF8, 0x77, 0x9E, 0x7F, 0x87, 0xC0, 0x1D, 0xE1, 0xFE,
    0x1C, 0x73, 0x87, 0x38, 0x73, 0x87, 0x38, 0xE3, 0x8E, 0x7F, 0xC7, 0xF8,
    0x60, 0x06, 0x00, 0x60, 0x0E, 0x00, 0x1E, 0xE7, 0xFD, 0xE7, 0x38, 0xEE,
    0x1D, 0xC3, 0xB8, 0x77, 0x1C, 0x7F, 0x8F, 0xF0, 0x0E, 0x01, 0x80, 0x30,
    0x06, 0x00, 0x3B, 0x36, 0x38, 0x70, 0x70, 0x70, 0x60, 0x60, 0xE0, 0xE0,
    0x3E, 0x3F, 0xF8, 0xFC, 0x0F, 0xC3, 0xF8, 0x3D, 0x8E, 0xFE, 0x3E, 0x00,
    0x38, 0xCF, 0xFE, 0x71, 0x86, 0x38, 0xE3, 0x8F, 0x3C, 0x31, 0xDC, 0x77,
    0x19, 0x86, 0x63, 0xB8, 0xEE, 0x33, 0x9C, 0xFF, 0x1F, 0xC0, 0xE1, 0x98,
    0xE6, 0x31, 0x9C, 0x66, 0x1B, 0x86, 0xC1, 0xF0, 0x78, 0x0E, 0x00, 0xE7,
    0x1B, 0x9C, 0xEE, 0x73, 0x3B, 0xDC, 0xEB, 0x63, 0xAD, 0x8F, 0xBC, 0x1C,
    0xF0, 0x73, 0xC1, 0xCE, 0x00, 0x1C, 0xE1, 0xCC, 0x0D, 0x80, 0xF8, 0x0F,
    0x00, 0xF0, 0x1F, 0x03, 0xB8, 0x33, 0x87, 0x38, 0x70, 0xCE, 0x38, 0xC6,
    0x19, 0xC3, 0x30, 0x66, 0x0F, 0x81, 0xF0, 0x3C, 0x03, 0x80, 0x60, 0x18,
    0x0F, 0x01, 0xC0, 0x00, 0x1F, 0xCF, 0xF0, 0x38, 0x1C, 0x0E, 0x07, 0x03,
    0x81, 0xC0, 0x7F, 0xBF, 0xE0, 0x0E, 0x38, 0x61, 0x83, 0x06, 0x0C, 0x78,
    0xF0, 0xC1, 0x83, 0x0E, 0x1C, 0x38, 0x78, 0x70, 0x18, 0xC4, 0x21, 0x18,
    0xC4, 0x21, 0x18, 0xC4, 0x23, 0x18, 0x80, 0x1C, 0x3C, 0x38, 0x70, 0xE1,
    0x83, 0x06, 0x1E, 0x5C, 0x60, 0xC1, 0x83, 0x0C, 0x38, 0xE0, 0x71, 0x8E};

const GFXglyph FreeSansBoldOblique9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 5, 0, 1},         // 0x20 ' '
    {0, 5, 13, 6, 2, -12},      // 0x21 '!'
    {9, 7, 5, 9, 3, -12},       // 0x22 '"'
    {14, 11, 12, 10, 1, -11},   // 0x23 '#'
    {31, 11, 16, 10, 1, -13},   // 0x24 '$'
    {53, 14, 13, 16, 2, -12},   // 0x25 '%'
    {76, 12, 13, 13, 2, -12},   // 0x26 '&'
    {96, 3, 5, 4, 3, -12},      // 0x27 '''
    {98, 8, 17, 6, 2, -12},     // 0x28 '('
    {115, 8, 17, 6, -2, -13},   // 0x29 ')'
    {132, 6, 6, 7, 3, -12},     // 0x2A '*'
    {137, 9, 8, 11, 2, -7},     // 0x2B '+'
    {146, 4, 6, 5, 0, -2},      // 0x2C ','
    {149, 5, 2, 6, 1, -5},      // 0x2D '-'
    {151, 3, 2, 5, 1, -1},      // 0x2E '.'
    {152, 8, 13, 5, 0, -12},    // 0x2F '/'
    {165, 10, 13, 10, 1, -12},  // 0x30 '0'
    {182, 7, 13, 10, 3, -12},   // 0x31 '1'
    {194, 11, 13, 10, 1, -12},  // 0x32 '2'
    {212, 11, 13, 10, 1, -12},  // 0x33 '3'
    {230, 10, 12, 10, 1, -11},  // 0x34 '4'
    {245, 11, 13, 10, 1, -12},  // 0x35 '5'
    {263, 10, 13, 10, 2, -12},  // 0x36 '6'
    {280, 10, 13, 10, 2, -12},  // 0x37 '7'
    {297, 11, 13, 10, 1, -12},  // 0x38 '8'
    {315, 11, 13, 10, 1, -12},  // 0x39 '9'
    {333, 4, 9, 6, 2, -8},      // 0x3A ':'
    {338, 5, 12, 6, 1, -8},     // 0x3B ';'
    {346, 10, 9, 11, 1, -8},    // 0x3C '<'
    {358, 10, 6, 11, 1, -6},    // 0x3D '='
    {366, 10, 9, 11, 1, -7},    // 0x3E '>'
    {378, 10, 13, 11, 3, -12},  // 0x3F '?'
    {395, 18, 16, 18, 1, -13},  // 0x40 '@'
    {431, 13, 13, 13, 0, -12},  // 0x41 'A'
    {453, 13, 13, 13, 1, -12},  // 0x42 'B'
    {475, 13, 13, 13, 2, -12},  // 0x43 'C'
    {497, 13, 13, 13, 1, -12},  // 0x44 'D'
    {519, 13, 13, 12, 1, -12},  // 0x45 'E'
    {541, 12, 13, 11, 1, -12},  // 0x46 'F'
    {561, 12, 13, 14, 2, -12},  // 0x47 'G'
    {581, 14, 13, 13, 1, -12},  // 0x48 'H'
    {604, 5, 13, 5, 1, -12},    // 0x49 'I'
    {613, 11, 13, 10, 1, -12},  // 0x4A 'J'
    {631, 14, 13, 13, 1, -12},  // 0x4B 'K'
    {654, 10, 13, 11, 1, -12},  // 0x4C 'L'
    {671, 16, 13, 15, 1, -12},  // 0x4D 'M'
    {697, 14, 13, 13, 1, -12},  // 0x4E 'N'
    {720, 13, 13, 14, 2, -12},  // 0x4F 'O'
    {742, 13, 13, 12, 1, -12},  // 0x50 'P'
    {764, 13, 14, 14, 2, -12},  // 0x51 'Q'
    {787, 13, 13, 13, 1, -12},  // 0x52 'R'
    {809, 13, 13, 12, 1, -12},  // 0x53 'S'
    {831, 11, 13, 11, 3, -12},  // 0x54 'T'
    {849, 13, 13, 13, 2, -12},  // 0x55 'U'
    {871, 11, 13, 12, 3, -12},  // 0x56 'V'
    {889, 17, 13, 17, 3, -12},  // 0x57 'W'
    {917, 14, 13, 12, 0, -12},  // 0x58 'X'
    {940, 11, 13, 12, 3, -12},  // 0x59 'Y'
    {958, 12, 13, 11, 1, -12},  // 0x5A 'Z'
    {978, 8, 17, 6, 0, -12},    // 0x5B '['
    {995, 3, 17, 5, 2, -16},    // 0x5C '\'
    {1002, 8, 17, 6, 0, -13},   // 0x5D ']'
    {1019, 8, 8, 11, 2, -12},   // 0x5E '^'
    {1027, 11, 1, 10, -1, 4},   // 0x5F '_'
    {1029, 3, 2, 6, 3, -12},    // 0x60 '`'
    {1030, 10, 10, 10, 1, -9},  // 0x61 'a'
    {1043, 11, 13, 11, 1, -12}, // 0x62 'b'
    {1061, 10, 10, 10, 1, -9},  // 0x63 'c'
    {1074, 12, 13, 11, 1, -12}, // 0x64 'd'
    {1094, 10, 10, 10, 1, -9},  // 0x65 'e'
    {1107, 6, 13, 6, 2, -12},   // 0x66 'f'
    {1117, 12, 14, 11, 0, -9},  // 0x67 'g'
    {1138, 11, 13, 11, 1, -12}, // 0x68 'h'
    {1156, 5, 13, 5, 1, -12},   // 0x69 'i'
    {1165, 8, 17, 5, -1, -12},  // 0x6A 'j'
    {1182, 11, 13, 10, 1, -12}, // 0x6B 'k'
    {1200, 5, 13, 5, 1, -12},   // 0x6C 'l'
    {1209, 16, 10, 16, 1, -9},  // 0x6D 'm'
    {1229, 11, 10, 11, 1, -9},  // 0x6E 'n'
    {1243, 11, 10, 11, 1, -9},  // 0x6F 'o'
    {1257, 12, 14, 11, 0, -9},  // 0x70 'p'
    {1278, 11, 14, 11, 1, -9},  // 0x71 'q'
    {1298, 8, 10, 7, 1, -9},    // 0x72 'r'
    {1308, 9, 10, 10, 2, -9},   // 0x73 's'
    {1320, 6, 12, 6, 2, -11},   // 0x74 't'
    {1329, 10, 10, 11, 2, -9},  // 0x75 'u'
    {1342, 10, 10, 10, 2, -9},  // 0x76 'v'
    {1355, 14, 10, 14, 2, -9},  // 0x77 'w'
    {1373, 12, 10, 10, 0, -9},  // 0x78 'x'
    {1388, 11, 14, 10, 1, -9},  // 0x79 'y'
    {1408, 10, 10, 9, 0, -9},   // 0x7A 'z'
    {1421, 7, 17, 7, 2, -12},   // 0x7B '{'
    {1436, 5, 17, 5, 1, -12},   // 0x7C '|'
    {1447, 7, 17, 7, 0, -13},   // 0x7D '}'
    {1462, 8, 2, 11, 2, -4}};   // 0x7E '~'

const GFXfont FreeSansBoldOblique9pt7b PROGMEM = {
    (uint8_t *)FreeSansBoldOblique9pt7bBitmaps,
    (GFXglyph *)FreeSansBoldOblique9pt7bGlyphs, 0x20, 0x7E, 22};

// Approx. 2136 bytes
