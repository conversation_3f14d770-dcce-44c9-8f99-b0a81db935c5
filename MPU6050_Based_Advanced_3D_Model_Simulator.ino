#include <WiFi.h>
#include <WebServer.h>
#include <WebSocketsServer.h>
#include <Wire.h>
#include <Adafruit_MPU6050.h>
#include <Adafruit_Sensor.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

// WiFi credentials
const char* ssid = "SKR";
const char* password = "12345678";

// Create objects
WebServer server(80);
WebSocketsServer webSocket(81);
Adafruit_MPU6050 mpu;

// Sensor data structure
struct SensorData {
  float roll, pitch, yaw;
  float accelX, accelY, accelZ;
  float gyroX, gyroY, gyroZ;
  float temperature;
};

SensorData sensorData;
unsigned long lastUpdate = 0;
const unsigned long UPDATE_INTERVAL = 25; // 40Hz update rate for smoother motion

// Enhanced complementary filter variables
float alpha = 0.98; // Higher alpha for more stable readings
float gyroAngleX = 0, gyroAngleY = 0, gyroAngleZ = 0;
float accAngleX = 0, accAngleY = 0;
float dt = 0;
unsigned long timer = 0;

// Calibration offsets
float gyroOffsetX = 0, gyroOffsetY = 0, gyroOffsetZ = 0;
float accelOffsetX = 0, accelOffsetY = 0, accelOffsetZ = 0;
bool isCalibrated = false;

// HTML page with enhanced UI
const char htmlPage[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Advanced MPU6050 Flight Simulator — SKR Electronics Lab</title>
  <script src="https://static.sketchfab.com/api/sketchfab-viewer-1.12.1.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    :root {
      --bg-dark: #0a0b0d;
      --bg-primary: #0f1419;
      --bg-secondary: #1a1f2e;
      --bg-tertiary: #242b3d;
      --panel-bg: rgba(26, 31, 46, 0.95);
      --panel-hover: rgba(36, 43, 61, 0.98);
      --accent: #00d4ff;
      --accent-dark: #0099cc;
      --accent-light: #4de1ff;
      --success: #00ff88;
      --warning: #ffaa00;
      --error: #ff4757;
      --text-primary: #ffffff;
      --text-secondary: #b8c5d6;
      --text-muted: #6b7684;
      --border: rgba(255,255,255,0.08);
      --border-light: rgba(255,255,255,0.12);
      --shadow-sm: 0 2px 8px rgba(0,0,0,0.15);
      --shadow-md: 0 8px 24px rgba(0,0,0,0.25);
      --shadow-lg: 0 16px 48px rgba(0,0,0,0.35);
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    }
    
    * { box-sizing: border-box; margin: 0; padding: 0; }
    
    html, body {
      height: 100vh;
      background: radial-gradient(ellipse at top, var(--bg-primary), var(--bg-dark));
      color: var(--text-primary);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      overflow-x: hidden;
      line-height: 1.5;
    }
    
    .container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Header */
    .header {
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border-bottom: 1px solid var(--border);
      padding: 16px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: var(--shadow-md);
      position: relative;
      z-index: 100;
    }
    
    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--accent), var(--accent-light));
    }
    
    .header-left h1 {
      font-size: 24px;
      font-weight: 700;
      background: linear-gradient(135deg, var(--accent), var(--accent-light));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -0.5px;
      margin-bottom: 2px;
    }
    
    .header-left p {
      color: var(--text-secondary);
      font-size: 13px;
      font-weight: 500;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .connection-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: var(--radius-md);
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .connection-status::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: left 0.6s;
    }
    
    .connection-status.connected {
      background: rgba(0, 255, 136, 0.15);
      border: 1px solid rgba(0, 255, 136, 0.3);
      color: var(--success);
    }
    
    .connection-status.connected::before {
      left: 100%;
    }
    
    .connection-status.disconnected {
      background: rgba(255, 71, 87, 0.15);
      border: 1px solid rgba(255, 71, 87, 0.3);
      color: var(--error);
    }
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.2); }
    }
    
    /* Main Content */
    .main-content {
      flex: 1;
      display: grid;
      grid-template-columns: 380px 1fr;
      height: calc(100vh - 72px);
      gap: 0;
    }
    
    /* Control Panel */
    .control-panel {
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border-right: 1px solid var(--border);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .panel-tabs {
      display: flex;
      border-bottom: 1px solid var(--border);
      background: var(--bg-tertiary);
    }
    
    .tab-button {
      flex: 1;
      padding: 16px 12px;
      background: none;
      border: none;
      color: var(--text-muted);
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
    }
    
    .tab-button.active {
      color: var(--accent);
    }
    
    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--accent);
    }
    
    .tab-button:hover:not(.active) {
      color: var(--text-secondary);
      background: rgba(255,255,255,0.05);
    }
    
    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .tab-panel {
      display: none;
      flex-direction: column;
      gap: 20px;
    }
    
    .tab-panel.active {
      display: flex;
    }
    
    /* Sensor Cards */
    .section-title {
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .section-title::before {
      content: '';
      width: 4px;
      height: 18px;
      background: var(--accent);
      border-radius: 2px;
    }
    
    .sensor-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }
    
    .sensor-card {
      background: rgba(255,255,255,0.03);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      padding: 16px;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .sensor-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--accent), var(--accent-light));
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    .sensor-card:hover {
      background: rgba(255,255,255,0.06);
      border-color: var(--border-light);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .sensor-card:hover::before {
      opacity: 1;
    }
    
    .sensor-label {
      font-size: 11px;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 8px;
      font-weight: 600;
    }
    
    .sensor-value {
      font-size: 24px;
      font-weight: 800;
      color: var(--text-primary);
      font-variant-numeric: tabular-nums;
      line-height: 1;
      display: flex;
      align-items: baseline;
    }
    
    .sensor-unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-left: 4px;
      font-weight: 500;
    }
    
    /* Full-width sensor cards */
    .sensor-card.full {
      grid-column: 1 / -1;
    }
    
    .temp-card {
      background: linear-gradient(135deg, rgba(255, 170, 0, 0.1), rgba(255, 170, 0, 0.05));
      border-color: rgba(255, 170, 0, 0.2);
    }
    
    /* Control Elements */
    .control-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .control-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }
    
    .control-label {
      font-size: 13px;
      color: var(--text-secondary);
      font-weight: 600;
      min-width: 80px;
    }
    
    .slider-container {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    input[type="range"] {
      flex: 1;
      -webkit-appearance: none;
      appearance: none;
      height: 6px;
      background: var(--bg-tertiary);
      border-radius: 3px;
      outline: none;
      transition: var(--transition);
    }
    
    input[type="range"]:hover {
      background: var(--bg-secondary);
    }
    
    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--accent);
      border: 2px solid #fff;
      box-shadow: var(--shadow-sm);
      cursor: pointer;
      transition: var(--transition);
    }
    
    input[type="range"]::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-md);
    }
    
    input[type="range"]::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--accent);
      border: 2px solid #fff;
      box-shadow: var(--shadow-sm);
      cursor: pointer;
    }
    
    .slider-value {
      min-width: 45px;
      font-size: 12px;
      color: var(--text-primary);
      font-weight: 600;
      text-align: right;
      font-variant-numeric: tabular-nums;
    }
    
    /* Buttons */
    .btn {
      padding: 12px 20px;
      border: 1px solid var(--border-light);
      border-radius: var(--radius-md);
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .btn:hover {
      background: var(--panel-hover);
      border-color: var(--border-light);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    .btn.primary {
      background: var(--accent);
      border-color: var(--accent);
      color: var(--bg-dark);
    }
    
    .btn.primary:hover {
      background: var(--accent-dark);
      border-color: var(--accent-dark);
    }
    
    .btn.success {
      background: var(--success);
      border-color: var(--success);
      color: var(--bg-dark);
    }
    
    .btn.warning {
      background: var(--warning);
      border-color: var(--warning);
      color: var(--bg-dark);
    }
    
    .btn-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }
    
    /* 3D Viewer */
    .viewer-container {
      background: var(--bg-dark);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    
    .viewer-frame {
      width: 100%;
      height: 100%;
      border: none;
      background: var(--bg-dark);
    }
    
    .viewer-overlay {
      position: absolute;
      top: 20px;
      right: 20px;
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      padding: 16px;
      font-size: 13px;
      color: var(--text-secondary);
      z-index: 10;
      opacity: 0;
      transform: translateY(-10px);
      transition: var(--transition);
      pointer-events: none;
    }
    
    .viewer-overlay.show {
      opacity: 1;
      transform: translateY(0);
    }
    
    .viewer-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 5;
      pointer-events: none;
    }
    
    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 3px solid var(--bg-tertiary);
      border-top: 3px solid var(--accent);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: var(--text-secondary);
      font-size: 16px;
      font-weight: 600;
    }
    
    /* Stats */
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 8px;
    }
    
    .stat-item {
      text-align: center;
      padding: 12px;
      background: rgba(255,255,255,0.03);
      border: 1px solid var(--border);
      border-radius: var(--radius-sm);
    }
    
    .stat-value {
      font-size: 18px;
      font-weight: 700;
      color: var(--accent);
      margin-bottom: 4px;
      font-variant-numeric: tabular-nums;
    }
    
    .stat-label {
      font-size: 11px;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }
    
    /* Responsive Design */
    @media (max-width: 1200px) {
      .main-content {
        grid-template-columns: 340px 1fr;
      }
    }
    
    @media (max-width: 1024px) {
      .main-content {
        grid-template-columns: 320px 1fr;
      }
      .panel-content {
        padding: 20px 16px;
      }
    }
    
    @media (max-width: 768px) {
      .header {
        padding: 12px 16px;
      }
      .header-left h1 {
        font-size: 20px;
      }
      .header-right {
        gap: 12px;
      }
      .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
      .control-panel {
        max-height: 300px;
        border-right: none;
        border-bottom: 1px solid var(--border);
      }
      .panel-content {
        padding: 16px;
        gap: 16px;
      }
      .sensor-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
      }
      .sensor-card {
        padding: 12px;
      }
      .sensor-value {
        font-size: 20px;
      }
      .stats-grid {
        grid-template-columns: 1fr 1fr;
      }
    }
    
    @media (max-width: 480px) {
      .header {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
      }
      .header-right {
        width: 100%;
        justify-content: center;
      }
      .sensor-grid {
        grid-template-columns: 1fr 1fr;
      }
      .btn-group {
        grid-template-columns: 1fr;
      }
      .control-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
      }
      .control-label {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="header-left">
        <h1>Advanced MPU6050 Flight Simulator</h1>
        <p>Real-time 3D visualization with enhanced precision • by SKR Electronics Lab</p>
      </div>
      <div class="header-right">
        <div id="connectionStatus" class="connection-status disconnected">
          <div class="status-dot"></div>
          <span>Connecting...</span>
        </div>
      </div>
    </header>
    
    <div class="main-content">
      <!-- Control Panel -->
      <div class="control-panel">
        <div class="panel-tabs">
          <button class="tab-button active" data-tab="sensors">Sensors</button>
          <button class="tab-button" data-tab="camera">Camera</button>
          <button class="tab-button" data-tab="settings">Settings</button>
        </div>
        
        <div class="panel-content">
          <!-- Sensors Tab -->
          <div class="tab-panel active" id="sensors-tab">
            <div class="section-title">Orientation Data</div>
            <div class="sensor-grid">
              <div class="sensor-card">
                <div class="sensor-label">Roll</div>
                <div class="sensor-value">
                  <span id="rollValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Pitch</div>
                <div class="sensor-value">
                  <span id="pitchValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Yaw</div>
                <div class="sensor-value">
                  <span id="yawValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card temp-card">
                <div class="sensor-label">Temperature</div>
                <div class="sensor-value">
                  <span id="tempValue">0.0</span><span class="sensor-unit">°C</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">Raw Sensor Data</div>
            <div class="sensor-grid">
              <div class="sensor-card">
                <div class="sensor-label">Accel X</div>
                <div class="sensor-value">
                  <span id="accelX">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Accel Y</div>
                <div class="sensor-value">
                  <span id="accelY">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Accel Z</div>
                <div class="sensor-value">
                  <span id="accelZ">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro X</div>
                <div class="sensor-value">
                  <span id="gyroX">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro Y</div>
                <div class="sensor-value">
                  <span id="gyroY">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro Z</div>
                <div class="sensor-value">
                  <span id="gyroZ">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">Statistics</div>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value" id="updateRate">0</div>
                <div class="stat-label">Updates/sec</div>
              </div>
              <div class="stat-item">
                <div class="stat-value" id="uptime">0</div>
                <div class="stat-label">Uptime</div>
              </div>
              <div class="stat-item">
                <div class="stat-value" id="dataPackets">0</div>
                <div class="stat-label">Packets</div>
              </div>
            </div>
          </div>
          
          <!-- Camera Tab -->
          <div class="tab-panel" id="camera-tab">
            <div class="section-title">Camera Controls</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Distance</span>
                <div class="slider-container">
                  <input type="range" id="cameraDistance" min="2" max="15" step="0.1" value="8">
                  <span class="slider-value" id="cameraDistanceValue">8.0</span>
                </div>
              </div>
              
              <div class="control-row">
                <span class="control-label">Height</span>
                <div class="slider-container">
                  <input type="range" id="cameraHeight" min="-5" max="10" step="0.1" value="2">
                  <span class="slider-value" id="cameraHeightValue">2.0</span>
                </div>
              </div>
              
              <div class="control-row">
                <span class="control-label">Smoothing</span>
                <div class="slider-container">
                  <input type="range" id="cameraSmoothing" min="0" max="0.5" step="0.01" value="0.1">
                  <span class="slider-value" id="cameraSmoothingValue">0.10</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">View Modes</div>
            <div class="btn-group">
              <button class="btn primary" id="followMode">Follow Mode</button>
              <button class="btn" id="orbitMode">Orbit Mode</button>
            </div>
            
            <div class="btn-group">
              <button class="btn" id="resetCamera">Reset Camera</button>
              <button class="btn" id="centerView">Center View</button>
            </div>
          </div>
          
          <!-- Settings Tab -->
          <div class="tab-panel" id="settings-tab">
            <div class="section-title">Sensor Settings</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Filter Alpha</span>
                <div class="slider-container">
                  <input type="range" id="filterAlpha" min="0.90" max="0.99" step="0.01" value="0.98">
                  <span class="slider-value" id="filterAlphaValue">0.98</span>
                </div>
              </div>
              
              <div class="control-row">
                <span class="control-label">Sensitivity</span>
                <div class="slider-container">
                  <input type="range" id="sensitivity" min="0.5" max="2.0" step="0.1" value="1.0">
                  <span class="slider-value" id="sensitivityValue">1.0</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">Calibration</div>
            <div class="btn-group">
              <button class="btn warning" id="calibrateSensor">Calibrate Sensor</button>
              <button class="btn success" id="resetOrientation">Reset Orientation</button>
            </div>
            
            <div class="section-title">Display Options</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Update Rate</span>
                <div class="slider-container">
                  <input type="range" id="updateRate" min="10" max="60" step="5" value="40">
                  <span class="slider-value" id="updateRateValue">40 Hz</span>
                </div>
              </div>
            </div>
            
            <div class="btn-group">
              <button class="btn" id="exportData">Export Data</button>
              <button class="btn" id="toggleStats">Toggle Stats</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 3D Viewer -->
      <div class="viewer-container">
        <iframe id="api-frame" class="viewer-frame"
          allow="autoplay; fullscreen; xr-spatial-tracking"
          xr-spatial-tracking execution-while-out-of-viewport execution-while-not-rendered web-share
          allowfullscreen mozallowfullscreen="true" webkitallowfullscreen="true">
        </iframe>
        
        <div id="viewerLoading" class="viewer-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading 3D Airplane Model...</div>
        </div>
        
        <div id="viewerOverlay" class="viewer-overlay">
          <div>Camera Mode: <span id="currentMode">Follow</span></div>
          <div>Distance: <span id="currentDistance">8.0</span>m</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Global variables
    let socket;
    let isConnected = false;
    let api = null;
    let isViewerReady = false;
    let currentMode = 'follow';
    let cameraSettings = {
      distance: 8,
      height: 2,
      smoothing: 0.1
    };
    let sensorSettings = {
      alpha: 0.98,
      sensitivity: 1.0,
      updateRate: 40
    };
    
    // Statistics
    let stats = {
      packets: 0,
      startTime: Date.now(),
      lastUpdateTime: 0,
      updateCount: 0
    };
    
    // Smooth camera transition
    let targetCamera = { x: 0, y: 2, z: 8 };
    let currentCamera = { x: 0, y: 2, z: 8 };
    
    // Tab Management
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const targetTab = button.dataset.tab;
        
        // Update active tab button
        document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
        button.classList.add('active');
        
        // Update active tab panel
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        document.getElementById(targetTab + '-tab').classList.add('active');
      });
    });
    
    // WebSocket Connection with enhanced reconnection
    const initWebSocket = () => {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = protocol + '//' + window.location.hostname + ':81';
      socket = new WebSocket(wsUrl);
      
      socket.onopen = () => {
        console.log('WebSocket connected');
        isConnected = true;
        updateConnectionStatus(true);
        stats.startTime = Date.now();
        stats.packets = 0;
      };
      
      socket.onclose = () => {
        console.log('WebSocket disconnected');
        isConnected = false;
        updateConnectionStatus(false);
        setTimeout(initWebSocket, 3000);
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateConnectionStatus(false);
      };
      
      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          updateSensorDisplay(data);
          updateAirplaneOrientation(data);
          updateStats();
        } catch (error) {
          console.error('Error parsing sensor data:', error);
        }
      };
    };
    
    const updateConnectionStatus = (connected) => {
      const statusEl = document.getElementById('connectionStatus');
      const statusText = statusEl.querySelector('span');
      
      if (connected) {
        statusText.textContent = 'Connected';
        statusEl.className = 'connection-status connected';
      } else {
        statusText.textContent = 'Disconnected';
        statusEl.className = 'connection-status disconnected';
      }
    };
    
    const updateSensorDisplay = (data) => {
      // Orientation data
      document.getElementById('rollValue').textContent = data.roll.toFixed(1);
      document.getElementById('pitchValue').textContent = data.pitch.toFixed(1);
      document.getElementById('yawValue').textContent = data.yaw.toFixed(1);
      
      // Raw sensor data
      if (data.accelX !== undefined) {
        document.getElementById('accelX').textContent = data.accelX.toFixed(2);
        document.getElementById('accelY').textContent = data.accelY.toFixed(2);
        document.getElementById('accelZ').textContent = data.accelZ.toFixed(2);
        document.getElementById('gyroX').textContent = data.gyroX.toFixed(1);
        document.getElementById('gyroY').textContent = data.gyroY.toFixed(1);
        document.getElementById('gyroZ').textContent = data.gyroZ.toFixed(1);
      }
      
      // Temperature
      if (data.temperature !== undefined) {
        document.getElementById('tempValue').textContent = data.temperature.toFixed(1);
      }
    };
    
    const updateStats = () => {
      stats.packets++;
      const now = Date.now();
      const elapsed = (now - stats.startTime) / 1000;
      const uptimeFormatted = elapsed < 60 ? 
        elapsed.toFixed(0) + 's' : 
        Math.floor(elapsed / 60) + 'm ' + (elapsed % 60).toFixed(0) + 's';
      
      // Calculate update rate
      if (now - stats.lastUpdateTime > 1000) {
        const rate = stats.updateCount;
        document.getElementById('updateRate').textContent = rate;
        stats.updateCount = 0;
        stats.lastUpdateTime = now;
      } else {
        stats.updateCount++;
      }
      
      document.getElementById('uptime').textContent = uptimeFormatted;
      document.getElementById('dataPackets').textContent = stats.packets;
    };
    
    // Enhanced Sketchfab Integration
    const iframe = document.getElementById('api-frame');
    const uid = '65df9818a34c44f3a77764d665408f8a';
    const client = new Sketchfab(iframe);
    
    client.init(uid, {
      ui_controls: 0,
      ui_infos: 0,
      ui_hint: 0,
      ui_animations: 0,
      ui_stop: 0,
      ui_inspector: 0,
      ui_vr: 0,
      ui_help: 0,
      ui_settings: 0,
      ui_fullscreen: 1,
      autostart: 1,
      preload: 1,
      camera: 0,
      success: (apiInstance) => {
        api = apiInstance;
        api.start();
        
        api.addEventListener('viewerready', () => {
          console.log('Sketchfab viewer ready');
          isViewerReady = true;
          document.getElementById('viewerLoading').style.display = 'none';
          document.getElementById('viewerOverlay').classList.add('show');
          api.setUserInteraction(false);
          resetCamera();
          startCameraAnimation();
        });
      },
      error: () => {
        console.error('Sketchfab viewer error');
        document.getElementById('viewerLoading').innerHTML = 
          '<div class="loading-text" style="color: var(--error);">Error loading 3D model</div>';
      }
    });
    
    // Enhanced math functions
    const deg2rad = (degrees) => degrees * Math.PI / 180;
    const rad2deg = (radians) => radians * 180 / Math.PI;
    const clamp = (value, min, max) => Math.max(min, Math.min(max, value));
    const lerp = (a, b, t) => a + (b - a) * t;
    
    // Enhanced camera functions
    const resetCamera = () => {
      if (!api || !isViewerReady) return;
      
      targetCamera = { x: 0, y: cameraSettings.height, z: cameraSettings.distance };
      currentCamera = { ...targetCamera };
      
      const eye = [currentCamera.x, currentCamera.y, currentCamera.z];
      const target = [0, 0, 0];
      
      api.setCameraLookAt(eye, target, 0, (err) => {
        if (err) console.warn('setCameraLookAt error:', err);
      });
      
      updateOverlay();
    };
    
    const updateAirplaneOrientation = (data) => {
      if (!api || !isViewerReady) return;
      
      if (currentMode === 'follow') {
        const yawDeg = -data.yaw * sensorSettings.sensitivity;
        const pitchDeg = clamp(-data.pitch * sensorSettings.sensitivity, -85, 85);
        const rollDeg = data.roll * sensorSettings.sensitivity; // For future roll implementation
        
        const yaw = deg2rad(yawDeg);
        const pitch = deg2rad(pitchDeg);
        
        // Calculate target camera position
        const distance = cameraSettings.distance;
        const height = cameraSettings.height;
        
        targetCamera.x = distance * Math.cos(pitch) * Math.sin(yaw);
        targetCamera.y = distance * Math.sin(pitch) + height;
        targetCamera.z = distance * Math.cos(pitch) * Math.cos(yaw);
      }
    };
    
    // Smooth camera animation loop
    const startCameraAnimation = () => {
      const animate = () => {
        if (!api || !isViewerReady) {
          requestAnimationFrame(animate);
          return;
        }
        
        // Smooth camera interpolation
        const smoothing = cameraSettings.smoothing;
        currentCamera.x = lerp(currentCamera.x, targetCamera.x, smoothing);
        currentCamera.y = lerp(currentCamera.y, targetCamera.y, smoothing);
        currentCamera.z = lerp(currentCamera.z, targetCamera.z, smoothing);
        
        const eye = [currentCamera.x, currentCamera.y, currentCamera.z];
        const target = [0, 0, 0];
        
        api.setCameraLookAt(eye, target, 0, (err) => {
          if (err) console.warn('setCameraLookAt error:', err);
        });
        
        requestAnimationFrame(animate);
      };
      
      animate();
    };
    
    const updateOverlay = () => {
      document.getElementById('currentMode').textContent = currentMode === 'follow' ? 'Follow' : 'Orbit';
      document.getElementById('currentDistance').textContent = cameraSettings.distance.toFixed(1);
    };
    
    // Settings Event Listeners
    const setupControls = () => {
      // Camera controls
      const distanceSlider = document.getElementById('cameraDistance');
      const heightSlider = document.getElementById('cameraHeight');
      const smoothingSlider = document.getElementById('cameraSmoothing');
      
      distanceSlider.addEventListener('input', (e) => {
        cameraSettings.distance = parseFloat(e.target.value);
        document.getElementById('cameraDistanceValue').textContent = cameraSettings.distance.toFixed(1);
        updateOverlay();
      });
      
      heightSlider.addEventListener('input', (e) => {
        cameraSettings.height = parseFloat(e.target.value);
        document.getElementById('cameraHeightValue').textContent = cameraSettings.height.toFixed(1);
      });
      
      smoothingSlider.addEventListener('input', (e) => {
        cameraSettings.smoothing = parseFloat(e.target.value);
        document.getElementById('cameraSmoothingValue').textContent = cameraSettings.smoothing.toFixed(2);
      });
      
      // Sensor settings
      const alphaSlider = document.getElementById('filterAlpha');
      const sensitivitySlider = document.getElementById('sensitivity');
      const updateRateSlider = document.getElementById('updateRate');
      
      alphaSlider.addEventListener('input', (e) => {
        sensorSettings.alpha = parseFloat(e.target.value);
        document.getElementById('filterAlphaValue').textContent = sensorSettings.alpha.toFixed(2);
        sendCommand('setAlpha', sensorSettings.alpha);
      });
      
      sensitivitySlider.addEventListener('input', (e) => {
        sensorSettings.sensitivity = parseFloat(e.target.value);
        document.getElementById('sensitivityValue').textContent = sensorSettings.sensitivity.toFixed(1);
      });
      
      updateRateSlider.addEventListener('input', (e) => {
        sensorSettings.updateRate = parseInt(e.target.value);
        document.getElementById('updateRateValue').textContent = sensorSettings.updateRate + ' Hz';
        sendCommand('setUpdateRate', sensorSettings.updateRate);
      });
      
      // Mode buttons
      document.getElementById('followMode').addEventListener('click', () => {
        currentMode = 'follow';
        api.setUserInteraction(false);
        updateModeButtons();
        updateOverlay();
      });
      
      document.getElementById('orbitMode').addEventListener('click', () => {
        currentMode = 'orbit';
        api.setUserInteraction(true);
        updateModeButtons();
        updateOverlay();
      });
      
      // Action buttons
      document.getElementById('resetCamera').addEventListener('click', resetCamera);
      document.getElementById('centerView').addEventListener('click', resetCamera);
      
      document.getElementById('calibrateSensor').addEventListener('click', () => {
        sendCommand('calibrate');
        showNotification('Calibrating sensor... Keep device steady for 3 seconds', 'warning');
      });
      
      document.getElementById('resetOrientation').addEventListener('click', () => {
        sendCommand('resetOrientation');
        showNotification('Orientation reset', 'success');
      });
      
      document.getElementById('exportData').addEventListener('click', exportSensorData);
      document.getElementById('toggleStats').addEventListener('click', toggleStatsDisplay);
    };
    
    const updateModeButtons = () => {
      const followBtn = document.getElementById('followMode');
      const orbitBtn = document.getElementById('orbitMode');
      
      if (currentMode === 'follow') {
        followBtn.classList.add('primary');
        orbitBtn.classList.remove('primary');
      } else {
        orbitBtn.classList.add('primary');
        followBtn.classList.remove('primary');
      }
    };
    
    const sendCommand = (command, value = null) => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const message = { command, value };
        socket.send(JSON.stringify(message));
      }
    };
    
    const showNotification = (message, type = 'info') => {
      // Create notification element
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 24px;
        background: var(--panel-bg);
        backdrop-filter: blur(24px);
        border: 1px solid var(--border);
        border-radius: var(--radius-md);
        padding: 16px 20px;
        color: var(--text-primary);
        font-size: 14px;
        font-weight: 600;
        z-index: 1000;
        transform: translateX(100%);
        transition: var(--transition);
        max-width: 300px;
        box-shadow: var(--shadow-lg);
      `;
      
      if (type === 'success') {
        notification.style.borderColor = 'rgba(0, 255, 136, 0.3)';
        notification.style.background = 'rgba(0, 255, 136, 0.1)';
      } else if (type === 'warning') {
        notification.style.borderColor = 'rgba(255, 170, 0, 0.3)';
        notification.style.background = 'rgba(255, 170, 0, 0.1)';
      } else if (type === 'error') {
        notification.style.borderColor = 'rgba(255, 71, 87, 0.3)';
        notification.style.background = 'rgba(255, 71, 87, 0.1)';
      }
      
      notification.textContent = message;
      document.body.appendChild(notification);
      
      // Animate in
      setTimeout(() => {
        notification.style.transform = 'translateX(0)';
      }, 100);
      
      // Remove after 3 seconds
      setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    };
    
    const exportSensorData = () => {
      const data = {
        timestamp: new Date().toISOString(),
        stats: stats,
        settings: { ...cameraSettings, ...sensorSettings },
        currentOrientation: {
          roll: parseFloat(document.getElementById('rollValue').textContent),
          pitch: parseFloat(document.getElementById('pitchValue').textContent),
          yaw: parseFloat(document.getElementById('yawValue').textContent)
        }
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mpu6050-data-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      showNotification('Data exported successfully', 'success');
    };
    
    const toggleStatsDisplay = () => {
      const statsGrid = document.querySelector('.stats-grid');
      const isVisible = statsGrid.style.display !== 'none';
      statsGrid.style.display = isVisible ? 'none' : 'grid';
      document.getElementById('toggleStats').textContent = isVisible ? 'Show Stats' : 'Hide Stats';
    };
    
    // Initialize everything
    window.addEventListener('load', () => {
      initWebSocket();
      setupControls();
      updateModeButtons();
      
      // Set initial slider values
      document.getElementById('cameraDistanceValue').textContent = cameraSettings.distance.toFixed(1);
      document.getElementById('cameraHeightValue').textContent = cameraSettings.height.toFixed(1);
      document.getElementById('cameraSmoothingValue').textContent = cameraSettings.smoothing.toFixed(2);
      document.getElementById('filterAlphaValue').textContent = sensorSettings.alpha.toFixed(2);
      document.getElementById('sensitivityValue').textContent = sensorSettings.sensitivity.toFixed(1);
      document.getElementById('updateRateValue').textContent = sensorSettings.updateRate + ' Hz';
    });
    
    // Reconnection on page focus
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !isConnected) {
        initWebSocket();
      }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'r' || e.key === 'R') {
        resetCamera();
      } else if (e.key === 'c' || e.key === 'C') {
        sendCommand('calibrate');
      } else if (e.key === ' ') {
        e.preventDefault();
        document.getElementById(currentMode === 'follow' ? 'orbitMode' : 'followMode').click();
      }
    });
  </script>
</body>
</html>
)rawliteral";

// Enhanced function declarations
void readSensors();
void broadcastSensorData();
void webSocketEvent(uint8_t num, WStype_t type, uint8_t * payload, size_t length);
void calibrateSensor();
void resetOrientation();
void handleCommand(String command, float value);

// Calibration variables
const int CALIBRATION_SAMPLES = 1000;
bool calibrationInProgress = false;

void setup() {
  Serial.begin(115200);
  
  // Initialize I2C with pull-up resistors
  Wire.begin(21, 22); // SDA=21, SCL=22
  Wire.setClock(400000); // 400kHz I2C speed
  
  // Initialize MPU6050 with error handling
  if (!mpu.begin()) {
    Serial.println("Failed to find MPU6050 chip");
    while (1) {
      delay(10);
    }
  }
  Serial.println("MPU6050 Found!");
  
  // Enhanced MPU6050 configuration
  mpu.setAccelerometerRange(MPU6050_RANGE_4_G); // More sensitive range
  mpu.setGyroRange(MPU6050_RANGE_250_DEG); // More precise gyro range
  mpu.setFilterBandwidth(MPU6050_BAND_94_HZ); // Higher bandwidth for responsiveness
  
  // Perform initial calibration
  Serial.println("Performing initial calibration...");
  calibrateSensor();
  
  // Connect to WiFi with enhanced feedback
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.print("Connected! IP address: ");
    Serial.println(WiFi.localIP());
    Serial.print("Signal strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
  } else {
    Serial.println("\nFailed to connect to WiFi!");
    return;
  }
  
  // Initialize WebSocket server with enhanced settings
  webSocket.begin();
  webSocket.onEvent(webSocketEvent);
  webSocket.enableHeartbeat(15000, 3000, 2); // Heartbeat for connection stability
  
  // Enhanced server routes
  server.on("/", HTTP_GET, [](){
    server.send_P(200, "text/html", htmlPage);
  });
  
  server.on("/status", HTTP_GET, [](){
    DynamicJsonDocument doc(512);
    doc["wifi_rssi"] = WiFi.RSSI();
    doc["free_heap"] = ESP.getFreeHeap();
    doc["uptime"] = millis();
    doc["calibrated"] = isCalibrated;
    doc["update_rate"] = 1000 / UPDATE_INTERVAL;
    
    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
  });
  
  // Start server
  server.begin();
  Serial.println("Web server started on port 80");
  Serial.println("WebSocket server started on port 81");
  Serial.println("Open your browser and go to: http://" + WiFi.localIP().toString());
  Serial.println("Press 'r' to reset camera, 'c' to calibrate, 'space' to toggle modes");
  
  timer = millis();
}

void loop() {
  server.handleClient();
  webSocket.loop();
  
  if (millis() - lastUpdate >= UPDATE_INTERVAL) {
    readSensors();
    broadcastSensorData();
    lastUpdate = millis();
  }
  
  // Non-blocking delay
  yield();
}

void readSensors() {
  sensors_event_t a, g, temp;
  mpu.getEvent(&a, &g, &temp);
  
  // Apply calibration offsets
  float ax = a.acceleration.x - accelOffsetX;
  float ay = a.acceleration.y - accelOffsetY;
  float az = a.acceleration.z - accelOffsetZ;
  float gx = g.gyro.x - gyroOffsetX;
  float gy = g.gyro.y - gyroOffsetY;
  float gz = g.gyro.z - gyroOffsetZ;
  
  // Calculate time delta with overflow protection
  unsigned long currentTime = millis();
  dt = (currentTime - timer) / 1000.0;
  timer = currentTime;
  
  // Limit dt to prevent integration errors
  if (dt > 0.1) dt = 0.1;
  
  // Enhanced angle calculations with better accuracy
  accAngleX = atan2(ay, sqrt(ax * ax + az * az)) * 180.0 / PI;
  accAngleY = atan2(-ax, sqrt(ay * ay + az * az)) * 180.0 / PI;
  
  // Gyroscope integration with drift compensation
  gyroAngleX += (gx * 180.0 / PI) * dt;
  gyroAngleY += (gy * 180.0 / PI) * dt;
  gyroAngleZ += (gz * 180.0 / PI) * dt;
  
  // Enhanced complementary filter with dynamic alpha
  float dynamicAlpha = alpha;
  
  // Reduce alpha when acceleration magnitude is close to 1g (more stable)
  float accelMagnitude = sqrt(ax * ax + ay * ay + az * az);
  if (abs(accelMagnitude - 9.81) < 2.0) {
    dynamicAlpha = alpha * 0.95; // Trust accelerometer more when stable
  }
  
  // Apply complementary filter
  sensorData.roll = dynamicAlpha * gyroAngleX + (1 - dynamicAlpha) * accAngleX;
  sensorData.pitch = dynamicAlpha * gyroAngleY + (1 - dynamicAlpha) * accAngleY;
  sensorData.yaw = gyroAngleZ; // Pure gyro integration for yaw
  
  // Update gyro angles for next iteration
  gyroAngleX = sensorData.roll;
  gyroAngleY = sensorData.pitch;
  
  // Store raw data
  sensorData.accelX = ax;
  sensorData.accelY = ay;
  sensorData.accelZ = az;
  sensorData.gyroX = gx * 180.0 / PI;
  sensorData.gyroY = gy * 180.0 / PI;
  sensorData.gyroZ = gz * 180.0 / PI;
  sensorData.temperature = temp.temperature;
}

void broadcastSensorData() {
  DynamicJsonDocument doc(512);
  doc["roll"] = round(sensorData.roll * 10) / 10.0; // Round to 1 decimal
  doc["pitch"] = round(sensorData.pitch * 10) / 10.0;
  doc["yaw"] = round(sensorData.yaw * 10) / 10.0;
  doc["accelX"] = round(sensorData.accelX * 100) / 100.0;
  doc["accelY"] = round(sensorData.accelY * 100) / 100.0;
  doc["accelZ"] = round(sensorData.accelZ * 100) / 100.0;
  doc["gyroX"] = round(sensorData.gyroX * 10) / 10.0;
  doc["gyroY"] = round(sensorData.gyroY * 10) / 10.0;
  doc["gyroZ"] = round(sensorData.gyroZ * 10) / 10.0;
  doc["temperature"] = round(sensorData.temperature * 10) / 10.0;
  doc["timestamp"] = millis();
  
  String jsonString;
  serializeJson(doc, jsonString);
  webSocket.broadcastTXT(jsonString);
}

void calibrateSensor() {
  Serial.println("Starting sensor calibration...");
  calibrationInProgress = true;
  
  float gyroSumX = 0, gyroSumY = 0, gyroSumZ = 0;
  float accelSumX = 0, accelSumY = 0, accelSumZ = 0;
  
  for (int i = 0; i < CALIBRATION_SAMPLES; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    
    gyroSumX += g.gyro.x;
    gyroSumY += g.gyro.y;
    gyroSumZ += g.gyro.z;
    accelSumX += a.acceleration.x;
    accelSumY += a.acceleration.y;
    accelSumZ += (a.acceleration.z - 9.81); // Assuming Z is up
    
    delay(2);
    
    if (i % 100 == 0) {
      Serial.print("Calibration progress: ");
      Serial.print((i * 100) / CALIBRATION_SAMPLES);
      Serial.println("%");
    }
  }
  
  // Calculate offsets
  gyroOffsetX = gyroSumX / CALIBRATION_SAMPLES;
  gyroOffsetY = gyroSumY / CALIBRATION_SAMPLES;
  gyroOffsetZ = gyroSumZ / CALIBRATION_SAMPLES;
  accelOffsetX = accelSumX / CALIBRATION_SAMPLES;
  accelOffsetY = accelSumY / CALIBRATION_SAMPLES;
  accelOffsetZ = accelSumZ / CALIBRATION_SAMPLES;
  
  isCalibrated = true;
  calibrationInProgress = false;
  
  Serial.println("Calibration complete!");
  Serial.printf("Gyro offsets: X=%.3f, Y=%.3f, Z=%.3f\n", gyroOffsetX, gyroOffsetY, gyroOffsetZ);
  Serial.printf("Accel offsets: X=%.3f, Y=%.3f, Z=%.3f\n", accelOffsetX, accelOffsetY, accelOffsetZ);
}

void resetOrientation() {
  gyroAngleX = 0;
  gyroAngleY = 0;
  gyroAngleZ = 0;
  timer = millis();
  Serial.println("Orientation reset");
}

void handleCommand(String command, float value) {
  if (command == "calibrate") {
    calibrateSensor();
  } else if (command == "resetOrientation") {
    resetOrientation();
  } else if (command == "setAlpha") {
    alpha = value;
    Serial.printf("Filter alpha set to: %.2f\n", alpha);
  } else if (command == "setUpdateRate") {
    // Note: This would require changing UPDATE_INTERVAL dynamically
    Serial.printf("Update rate change requested: %.0f Hz\n", value);
  }
}

void webSocketEvent(uint8_t num, WStype_t type, uint8_t * payload, size_t length) {
  switch(type) {
    case WStype_DISCONNECTED:
      Serial.printf("Client [%u] disconnected\n", num);
      break;

    case WStype_CONNECTED:
      {
        IPAddress ip = webSocket.remoteIP(num);
        Serial.printf("Client [%u] connected from %s\n", num, ip.toString().c_str());

        // Send an initial status packet to the newly connected client
        DynamicJsonDocument doc(256);
        doc["type"] = "status";
        doc["calibrated"] = isCalibrated;
        doc["update_interval_ms"] = UPDATE_INTERVAL;
        String out;
        serializeJson(doc, out);
        webSocket.sendTXT(num, out);
      }
      break;

    case WStype_TEXT:
      {
        // Handle incoming text messages (expect JSON commands)
        String msg = String((char*)payload);
        Serial.printf("Received from [%u]: %s\n", num, msg.c_str());

        DynamicJsonDocument cmdDoc(512);
        DeserializationError err = deserializeJson(cmdDoc, msg);
        if (err) {
          Serial.println("Failed to parse incoming JSON command");
          break;
        }

        // Support either {"command":"calibrate"} or {"type":"getStatus"}
        if (cmdDoc.containsKey("command")) {
          String command = cmdDoc["command"].as<String>();
          float value = 0.0;
          if (cmdDoc.containsKey("value")) value = cmdDoc["value"].as<float>();
          handleCommand(command, value);
        } else if (cmdDoc.containsKey("type") && String(cmdDoc["type"].as<const char*>()) == "getStatus") {
          DynamicJsonDocument resp(256);
          resp["type"] = "status";
          resp["calibrated"] = isCalibrated;
          resp["update_interval_ms"] = UPDATE_INTERVAL;
          String respStr;
          serializeJson(resp, respStr);
          webSocket.sendTXT(num, respStr);
        }
      }
      break;

    case WStype_PING:
    case WStype_PONG:
      // Let the WebSocketsServer library handle heartbeat
      break;

    default:
      break;
  }
}