# Contributing to MotionSync Pro 3D

Thank you for your interest in contributing to MotionSync Pro 3D! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### 🐛 Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates.

**When reporting bugs, please include:**
- **Device information**: ESP32 model, MPU6050 version
- **Environment**: Arduino IDE version, library versions
- **Steps to reproduce**: Clear, step-by-step instructions
- **Expected behavior**: What you expected to happen
- **Actual behavior**: What actually happened
- **Serial output**: Include relevant console logs
- **Screenshots**: If applicable

**Bug Report Template:**
```markdown
**Device Setup:**
- ESP32 Model: [e.g., ESP32-WROOM-32]
- MPU6050 Version: [e.g., GY-521]
- Arduino IDE: [e.g., 2.2.1]
- Firmware Version: [e.g., 2.0.0]

**Bug Description:**
A clear description of the bug.

**Steps to Reproduce:**
1. Step one
2. Step two
3. Step three

**Expected Behavior:**
What should happen.

**Actual Behavior:**
What actually happens.

**Additional Context:**
Any other relevant information.
```

### 💡 Suggesting Features

We welcome feature suggestions! Please:

1. **Check existing issues** for similar requests
2. **Describe the feature** clearly and concisely
3. **Explain the use case** - why is this feature needed?
4. **Consider implementation** - how might it work?
5. **Think about compatibility** - will it affect existing functionality?

### 🔧 Code Contributions

#### Development Environment Setup

1. **Install Arduino IDE** (2.0 or later)
2. **Add ESP32 board support**:
   - File → Preferences → Additional Board Manager URLs
   - Add: `https://dl.espressif.com/dl/package_esp32_index.json`
   - Tools → Board → Boards Manager → Search "ESP32" → Install

3. **Install required libraries**:
   ```
   - WebSocketsServer by Markus Sattler
   - ArduinoJson by Benoit Blanchon  
   - Adafruit MPU6050 by Adafruit
   - Adafruit Sensor by Adafruit
   ```

4. **Hardware setup**:
   - ESP32 development board
   - MPU6050 sensor module
   - Breadboard and jumper wires

#### Code Style Guidelines

**General Principles:**
- Write clean, readable code
- Use meaningful variable and function names
- Add comments for complex logic
- Follow existing code patterns
- Test on real hardware

**Naming Conventions:**
```cpp
// Variables: camelCase
int sensorValue;
float motionThreshold;

// Functions: camelCase
void readSensors();
void updateDisplay();

// Constants: UPPER_SNAKE_CASE
const int UPDATE_INTERVAL = 25;
const char* DEFAULT_SSID = "MotionSync-Pro-3D";

// Classes/Structs: PascalCase
struct SensorData {
  float roll, pitch, yaw;
};
```

**Code Formatting:**
- Use 2 spaces for indentation
- Place opening braces on the same line
- Add spaces around operators
- Keep lines under 100 characters when possible

**Comments:**
```cpp
// Single line comments for brief explanations
void calibrateSensor(); // Brief description

/*
 * Multi-line comments for detailed explanations
 * of complex algorithms or important sections
 */
```

#### Pull Request Process

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**:
   - Follow code style guidelines
   - Add/update comments
   - Test thoroughly

4. **Commit your changes**:
   ```bash
   git commit -m "Add: Brief description of changes"
   ```
   
   **Commit Message Format:**
   - `Add:` for new features
   - `Fix:` for bug fixes
   - `Update:` for improvements
   - `Remove:` for deletions
   - `Docs:` for documentation

5. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**:
   - Use a clear, descriptive title
   - Describe what changes were made and why
   - Reference any related issues
   - Include testing information

#### Testing Guidelines

**Before submitting:**
- [ ] Code compiles without errors or warnings
- [ ] Tested on actual ESP32 hardware
- [ ] All existing features still work
- [ ] New features work as expected
- [ ] Memory usage is reasonable
- [ ] No performance regressions

**Testing Checklist:**
- [ ] WiFi AP mode starts correctly
- [ ] Web interface loads properly
- [ ] Sensor data updates in real-time
- [ ] 3D model loading works
- [ ] Settings save and load correctly
- [ ] Multiple clients can connect
- [ ] Error handling works properly

## 📝 Documentation

### Updating Documentation

When making changes that affect:
- **User interface**: Update README.md
- **API**: Update API documentation
- **Configuration**: Update setup instructions
- **New features**: Add to CHANGELOG.md

### Documentation Style

- Use clear, concise language
- Include code examples where helpful
- Add screenshots for UI changes
- Keep formatting consistent
- Test all instructions

## 🏗️ Project Structure

```
MotionSync-Pro-3D/
├── MPU6050_Based_Advanced_3D_Model_Simulator.ino  # Main firmware
├── README.md                                       # Project documentation
├── CHANGELOG.md                                    # Version history
├── LICENSE                                         # License information
├── CONTRIBUTING.md                                 # This file
└── docs/                                          # Additional documentation
    ├── hardware-guide.md                          # Hardware setup guide
    ├── api-reference.md                           # API documentation
    └── troubleshooting.md                         # Common issues
```

## 🎯 Areas for Contribution

### High Priority
- **Bug fixes**: Stability and reliability improvements
- **Performance optimization**: Memory and speed improvements
- **Documentation**: Setup guides and tutorials
- **Testing**: Hardware compatibility testing

### Medium Priority
- **New features**: Enhanced functionality
- **UI improvements**: Better user experience
- **Code refactoring**: Cleaner, more maintainable code
- **Examples**: Sample projects and use cases

### Low Priority
- **Advanced features**: Experimental functionality
- **Platform support**: Additional hardware platforms
- **Integrations**: Third-party service integration

## 🌟 Recognition

Contributors will be recognized in:
- **README.md**: Contributors section
- **CHANGELOG.md**: Feature attribution
- **Release notes**: Major contribution highlights

## 📞 Getting Help

**Need help contributing?**
- 📧 **Email**: <EMAIL>
- 🌐 **Website**: https://www.skrelectronicslab.com
- 💬 **Discussions**: Use GitHub Discussions for questions

**Response Times:**
- Bug reports: 1-3 business days
- Feature requests: 1-7 business days
- Pull requests: 1-5 business days

## 📋 Code of Conduct

### Our Standards

- **Be respectful**: Treat everyone with respect and kindness
- **Be inclusive**: Welcome people of all backgrounds and experience levels
- **Be constructive**: Provide helpful feedback and suggestions
- **Be patient**: Remember that everyone is learning
- **Be professional**: Maintain a professional tone in all interactions

### Unacceptable Behavior

- Harassment or discrimination of any kind
- Offensive, inappropriate, or unprofessional language
- Personal attacks or insults
- Spam or off-topic discussions
- Sharing private information without permission

### Enforcement

Violations of the code of conduct should be reported to:
- 📧 **Email**: <EMAIL>

All reports will be reviewed and investigated promptly and fairly.

---

## 🙏 Thank You

Thank you for contributing to MotionSync Pro 3D! Your contributions help make this project better for everyone.

**Happy coding!** 🚀

---

*Developed with ❤️ by [SKR Electronics Lab](https://www.skrelectronicslab.com)*
