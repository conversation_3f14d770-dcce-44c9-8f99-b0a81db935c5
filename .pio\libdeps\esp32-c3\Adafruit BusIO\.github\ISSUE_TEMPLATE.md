Thank you for opening an issue on an Adafruit Arduino library repository.  To
improve the speed of resolution please review the following guidelines and
common troubleshooting steps below before creating the issue:

- **Do not use GitHub issues for troubleshooting projects and issues.**  Instead use
  the forums at http://forums.adafruit.com to ask questions and troubleshoot why
  something isn't working as expected.  In many cases the problem is a common issue
  that you will more quickly receive help from the forum community.  GitHub issues
  are meant for known defects in the code.  If you don't know if there is a defect
  in the code then start with troubleshooting on the forum first.

- **If following a tutorial or guide be sure you didn't miss a step.** Carefully
  check all of the steps and commands to run have been followed.  Consult the
  forum if you're unsure or have questions about steps in a guide/tutorial.

- **For Arduino projects check these very common issues to ensure they don't apply**:

  - For uploading sketches or communicating with the board make sure you're using
    a **USB data cable** and **not** a **USB charge-only cable**.  It is sometimes
    very hard to tell the difference between a data and charge cable!  Try using the
    cable with other devices or swapping to another cable to confirm it is not
    the problem.

  - **Be sure you are supplying adequate power to the board.**  Check the specs of
    your board and plug in an external power supply.  In many cases just
    plugging a board into your computer is not enough to power it and other
    peripherals.

  - **Double check all soldering joints and connections.**  Flakey connections
    cause many mysterious problems.  See the [guide to excellent soldering](https://learn.adafruit.com/adafruit-guide-excellent-soldering/tools) for examples of good solder joints.

  - **Ensure you are using an official Arduino or Adafruit board.** We can't
    guarantee a clone board will have the same functionality and work as expected
    with this code and don't support them.

If you're sure this issue is a defect in the code and checked the steps above
please fill in the following fields to provide enough troubleshooting information.
You may delete the guideline and text above to just leave the following details:

- Arduino board:  **INSERT ARDUINO BOARD NAME/TYPE HERE**

- Arduino IDE version (found in Arduino -> About Arduino menu):  **INSERT ARDUINO
  VERSION HERE**

- List the steps to reproduce the problem below (if possible attach a sketch or
  copy the sketch code in too): **LIST REPRO STEPS BELOW**
