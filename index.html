<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <title>MotionSync Pro 3D — Professional Motion Controller</title>
  <meta name="description" content="Professional-grade 3D model motion controller powered by ESP32 and MPU6050">
  <meta name="author" content="SKR Electronics Lab">
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎮%3C/text%3E%3C/svg%3E">
  <script src="https://static.sketchfab.com/api/sketchfab-viewer-1.12.1.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --bg-dark: #0a0b0d;
      --bg-primary: #0f1419;
      --bg-secondary: #1a1f2e;
      --bg-tertiary: #242b3d;
      --bg-quaternary: #2d3748;
      --panel-bg: rgba(26, 31, 46, 0.95);
      --panel-hover: rgba(36, 43, 61, 0.98);
      --accent: #00d4ff;
      --accent-dark: #0099cc;
      --accent-light: #4de1ff;
      --accent-gradient: linear-gradient(135deg, #00d4ff, #4de1ff);
      --success: #00ff88;
      --success-dark: #00cc6a;
      --warning: #ffaa00;
      --warning-dark: #cc8800;
      --error: #ff4757;
      --error-dark: #cc3a47;
      --info: #667eea;
      --text-primary: #ffffff;
      --text-secondary: #b8c5d6;
      --text-muted: #6b7684;
      --text-accent: #00d4ff;
      --border: rgba(255,255,255,0.08);
      --border-light: rgba(255,255,255,0.12);
      --border-accent: rgba(0, 212, 255, 0.3);
      --shadow-sm: 0 2px 8px rgba(0,0,0,0.15);
      --shadow-md: 0 8px 24px rgba(0,0,0,0.25);
      --shadow-lg: 0 16px 48px rgba(0,0,0,0.35);
      --shadow-xl: 0 24px 64px rgba(0,0,0,0.45);
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 20px;
      --transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
      --transition-fast: all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
      --transition-slow: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
      --glow: 0 0 20px rgba(0, 212, 255, 0.3);
      --glow-strong: 0 0 30px rgba(0, 212, 255, 0.5);
    }
    
    * { box-sizing: border-box; margin: 0; padding: 0; }
    
    html, body {
      height: 100vh;
      background: radial-gradient(ellipse at top, var(--bg-primary), var(--bg-dark));
      color: var(--text-primary);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      overflow-x: hidden;
      line-height: 1.5;
    }
    
    .container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Header */
    .header {
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border-bottom: 1px solid var(--border);
      padding: 16px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: var(--shadow-md);
      position: relative;
      z-index: 100;
    }
    
    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--accent), var(--accent-light));
    }
    
    .header-left h1 {
      font-size: 28px;
      font-weight: 900;
      background: var(--accent-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.8px;
      margin-bottom: 4px;
      text-shadow: var(--glow);
      position: relative;
    }

    .header-left h1::after {
      content: '™';
      font-size: 12px;
      position: absolute;
      top: 0;
      margin-left: 4px;
      color: var(--accent);
      font-weight: 600;
    }

    .header-left p {
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .header-left .brand-link {
      color: var(--accent);
      text-decoration: none;
      font-size: 12px;
      font-weight: 600;
      transition: var(--transition-fast);
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .header-left .brand-link:hover {
      color: var(--accent-light);
      text-shadow: var(--glow);
    }

    .header-left .brand-link i {
      font-size: 10px;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .connection-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: var(--radius-md);
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .connection-status::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: left 0.6s;
    }
    
    .connection-status.connected {
      background: rgba(0, 255, 136, 0.15);
      border: 1px solid rgba(0, 255, 136, 0.3);
      color: var(--success);
    }
    
    .connection-status.connected::before {
      left: 100%;
    }
    
    .connection-status.disconnected {
      background: rgba(255, 71, 87, 0.15);
      border: 1px solid rgba(255, 71, 87, 0.3);
      color: var(--error);
    }
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.2); }
    }
    
    /* Main Content */
    .main-content {
      flex: 1;
      display: grid;
      grid-template-columns: 380px 1fr;
      height: calc(100vh - 72px);
      gap: 0;
    }
    
    /* Control Panel */
    .control-panel {
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border-right: 1px solid var(--border);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .panel-tabs {
      display: flex;
      border-bottom: 1px solid var(--border);
      background: var(--bg-tertiary);
    }
    
    .tab-button {
      flex: 1;
      padding: 16px 12px;
      background: none;
      border: none;
      color: var(--text-muted);
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
    }
    
    .tab-button.active {
      color: var(--accent);
    }
    
    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--accent);
    }
    
    .tab-button:hover:not(.active) {
      color: var(--text-secondary);
      background: rgba(255,255,255,0.05);
    }
    
    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .tab-panel {
      display: none;
      flex-direction: column;
      gap: 20px;
    }
    
    .tab-panel.active {
      display: flex;
    }
    
    /* Sensor Cards */
    .section-title {
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .section-title::before {
      content: '';
      width: 4px;
      height: 18px;
      background: var(--accent);
      border-radius: 2px;
    }
    
    .sensor-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }
    
    .sensor-card {
      background: rgba(255,255,255,0.03);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      padding: 16px;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .sensor-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--accent), var(--accent-light));
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    .sensor-card:hover {
      background: rgba(255,255,255,0.06);
      border-color: var(--border-light);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .sensor-card:hover::before {
      opacity: 1;
    }
    
    .sensor-label {
      font-size: 11px;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 8px;
      font-weight: 600;
    }
    
    .sensor-value {
      font-size: 24px;
      font-weight: 800;
      color: var(--text-primary);
      font-variant-numeric: tabular-nums;
      line-height: 1;
      display: flex;
      align-items: baseline;
    }
    
    .sensor-unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-left: 4px;
      font-weight: 500;
    }
    
    /* Full-width sensor cards */
    .sensor-card.full {
      grid-column: 1 / -1;
    }
    
    .temp-card {
      background: linear-gradient(135deg, rgba(255, 170, 0, 0.1), rgba(255, 170, 0, 0.05));
      border-color: rgba(255, 170, 0, 0.2);
    }
    
    /* Control Elements */
    .control-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .control-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }
    
    .control-label {
      font-size: 13px;
      color: var(--text-secondary);
      font-weight: 600;
      min-width: 80px;
    }
    
    .slider-container {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    input[type="range"] {
      flex: 1;
      -webkit-appearance: none;
      appearance: none;
      height: 6px;
      background: var(--bg-tertiary);
      border-radius: 3px;
      outline: none;
      transition: var(--transition);
    }
    
    input[type="range"]:hover {
      background: var(--bg-secondary);
    }
    
    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--accent);
      border: 2px solid #fff;
      box-shadow: var(--shadow-sm);
      cursor: pointer;
      transition: var(--transition);
    }
    
    input[type="range"]::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-md);
    }
    
    input[type="range"]::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--accent);
      border: 2px solid #fff;
      box-shadow: var(--shadow-sm);
      cursor: pointer;
    }
    
    .slider-value {
      min-width: 45px;
      font-size: 12px;
      color: var(--text-primary);
      font-weight: 600;
      text-align: right;
      font-variant-numeric: tabular-nums;
    }
    
    /* Buttons */
    .btn {
      padding: 12px 20px;
      border: 1px solid var(--border-light);
      border-radius: var(--radius-md);
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .btn:hover {
      background: var(--panel-hover);
      border-color: var(--border-light);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    .btn.primary {
      background: var(--accent);
      border-color: var(--accent);
      color: var(--bg-dark);
    }
    
    .btn.primary:hover {
      background: var(--accent-dark);
      border-color: var(--accent-dark);
    }
    
    .btn.success {
      background: var(--success);
      border-color: var(--success);
      color: var(--bg-dark);
    }
    
    .btn.warning {
      background: var(--warning);
      border-color: var(--warning);
      color: var(--bg-dark);
    }
    
    .btn-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }
    
    /* 3D Viewer */
    .viewer-container {
      background: var(--bg-dark);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    
    .viewer-frame {
      width: 100%;
      height: 100%;
      border: none;
      background: var(--bg-dark);
    }
    
    .viewer-overlay {
      position: absolute;
      top: 20px;
      right: 20px;
      background: var(--panel-bg);
      backdrop-filter: blur(24px);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      padding: 16px;
      font-size: 13px;
      color: var(--text-secondary);
      z-index: 10;
      opacity: 0;
      transform: translateY(-10px);
      transition: var(--transition);
      pointer-events: none;
    }
    
    .viewer-overlay.show {
      opacity: 1;
      transform: translateY(0);
    }
    
    .viewer-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 5;
      pointer-events: none;
    }
    
    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 3px solid var(--bg-tertiary);
      border-top: 3px solid var(--accent);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: var(--text-secondary);
      font-size: 16px;
      font-weight: 600;
    }
    
    /* Stats */
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 8px;
    }
    
    .stat-item {
      text-align: center;
      padding: 12px;
      background: rgba(255,255,255,0.03);
      border: 1px solid var(--border);
      border-radius: var(--radius-sm);
    }
    
    .stat-value {
      font-size: 18px;
      font-weight: 700;
      color: var(--accent);
      margin-bottom: 4px;
      font-variant-numeric: tabular-nums;
    }
    
    .stat-label {
      font-size: 11px;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }
    
    /* Model Selection Styles */
    .model-input-group {
      margin-bottom: 20px;
    }

    .input-with-button {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
    }

    .model-input {
      flex: 1;
      padding: 12px 16px;
      background: var(--bg-tertiary);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      font-size: 14px;
      font-family: 'Inter', monospace;
      transition: var(--transition);
    }

    .model-input:focus {
      outline: none;
      border-color: var(--accent);
      box-shadow: var(--glow);
    }

    .help-text {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: var(--text-muted);
    }

    .help-text a {
      color: var(--accent);
      text-decoration: none;
      font-weight: 600;
    }

    .help-text a:hover {
      color: var(--accent-light);
    }

    .preset-models {
      margin-top: 16px;
    }

    .model-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-top: 8px;
    }

    .model-preset {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px 12px;
      background: var(--bg-tertiary);
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      color: var(--text-secondary);
      cursor: pointer;
      transition: var(--transition);
      font-size: 12px;
      font-weight: 600;
    }

    .model-preset:hover {
      background: var(--panel-hover);
      border-color: var(--accent);
      color: var(--accent);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .model-preset.active {
      background: rgba(0, 212, 255, 0.1);
      border-color: var(--accent);
      color: var(--accent);
    }

    .model-preset i {
      font-size: 20px;
    }

    /* Toggle Switch */
    .toggle-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 48px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-tertiary);
      border: 1px solid var(--border);
      transition: var(--transition);
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 2px;
      bottom: 2px;
      background: var(--text-muted);
      transition: var(--transition);
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background: var(--accent);
      border-color: var(--accent);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(24px);
      background: white;
    }

    /* Header Stats */
    .header-stats {
      display: flex;
      gap: 16px;
      margin-right: 16px;
    }

    .header-stats .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
    }

    .header-stats .stat-label {
      font-size: 10px;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }

    .header-stats .stat-value {
      font-size: 16px;
      color: var(--accent);
      font-weight: 700;
      font-variant-numeric: tabular-nums;
    }

    /* Enhanced Button Icons */
    .btn i {
      margin-right: 6px;
      font-size: 12px;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .main-content {
        grid-template-columns: 340px 1fr;
      }
      .model-grid {
        grid-template-columns: 1fr;
      }
    }
    
    @media (max-width: 1024px) {
      .main-content {
        grid-template-columns: 320px 1fr;
      }
      .panel-content {
        padding: 20px 16px;
      }
    }
    
    @media (max-width: 768px) {
      .header {
        padding: 12px 16px;
      }
      .header-left h1 {
        font-size: 20px;
      }
      .header-right {
        gap: 12px;
      }
      .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
      .control-panel {
        max-height: 300px;
        border-right: none;
        border-bottom: 1px solid var(--border);
      }
      .panel-content {
        padding: 16px;
        gap: 16px;
      }
      .sensor-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
      }
      .sensor-card {
        padding: 12px;
      }
      .sensor-value {
        font-size: 20px;
      }
      .stats-grid {
        grid-template-columns: 1fr 1fr;
      }
    }
    
    @media (max-width: 480px) {
      .header {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
      }
      .header-right {
        width: 100%;
        justify-content: center;
      }
      .sensor-grid {
        grid-template-columns: 1fr 1fr;
      }
      .btn-group {
        grid-template-columns: 1fr;
      }
      .control-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
      }
      .control-label {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="header-left">
        <h1>MotionSync Pro 3D</h1>
        <p>Professional Motion Controller • Real-time 3D Model Synchronization</p>
        <a href="https://www.skrelectronicslab.com" target="_blank" class="brand-link">
          <i class="fas fa-external-link-alt"></i>
          by SKR Electronics Lab
        </a>
      </div>
      <div class="header-right">
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-label">FPS</span>
            <span class="stat-value" id="fpsDisplay">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Clients</span>
            <span class="stat-value" id="clientCount">0</span>
          </div>
        </div>
        <div id="connectionStatus" class="connection-status disconnected">
          <div class="status-dot"></div>
          <span>Connecting...</span>
        </div>
      </div>
    </header>
    
    <div class="main-content">
      <!-- Control Panel -->
      <div class="control-panel">
        <div class="panel-tabs">
          <button class="tab-button active" data-tab="sensors">Sensors</button>
          <button class="tab-button" data-tab="camera">Camera</button>
          <button class="tab-button" data-tab="settings">Settings</button>
        </div>
        
        <div class="panel-content">
          <!-- Sensors Tab -->
          <div class="tab-panel active" id="sensors-tab">
            <div class="section-title">Orientation Data</div>
            <div class="sensor-grid">
              <div class="sensor-card">
                <div class="sensor-label">Roll</div>
                <div class="sensor-value">
                  <span id="rollValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Pitch</div>
                <div class="sensor-value">
                  <span id="pitchValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Yaw</div>
                <div class="sensor-value">
                  <span id="yawValue">0.0</span><span class="sensor-unit">°</span>
                </div>
              </div>
              
              <div class="sensor-card temp-card">
                <div class="sensor-label">Temperature</div>
                <div class="sensor-value">
                  <span id="tempValue">0.0</span><span class="sensor-unit">°C</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">Raw Sensor Data</div>
            <div class="sensor-grid">
              <div class="sensor-card">
                <div class="sensor-label">Accel X</div>
                <div class="sensor-value">
                  <span id="accelX">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Accel Y</div>
                <div class="sensor-value">
                  <span id="accelY">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Accel Z</div>
                <div class="sensor-value">
                  <span id="accelZ">0.0</span><span class="sensor-unit">m/s²</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro X</div>
                <div class="sensor-value">
                  <span id="gyroX">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro Y</div>
                <div class="sensor-value">
                  <span id="gyroY">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
              
              <div class="sensor-card">
                <div class="sensor-label">Gyro Z</div>
                <div class="sensor-value">
                  <span id="gyroZ">0.0</span><span class="sensor-unit">°/s</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">Statistics</div>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value" id="updateRate">0</div>
                <div class="stat-label">Updates/sec</div>
              </div>
              <div class="stat-item">
                <div class="stat-value" id="uptime">0</div>
                <div class="stat-label">Uptime</div>
              </div>
              <div class="stat-item">
                <div class="stat-value" id="dataPackets">0</div>
                <div class="stat-label">Packets</div>
              </div>
            </div>
          </div>
          
          <!-- Camera Tab -->
          <div class="tab-panel" id="camera-tab">
            <div class="section-title">Camera Controls</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Distance</span>
                <div class="slider-container">
                  <input type="range" id="cameraDistance" min="2" max="15" step="0.1" value="8">
                  <span class="slider-value" id="cameraDistanceValue">8.0</span>
                </div>
              </div>
              
              <div class="control-row">
                <span class="control-label">Height</span>
                <div class="slider-container">
                  <input type="range" id="cameraHeight" min="-5" max="10" step="0.1" value="2">
                  <span class="slider-value" id="cameraHeightValue">2.0</span>
                </div>
              </div>
              
              <div class="control-row">
                <span class="control-label">Smoothing</span>
                <div class="slider-container">
                  <input type="range" id="cameraSmoothing" min="0" max="0.5" step="0.01" value="0.1">
                  <span class="slider-value" id="cameraSmoothingValue">0.10</span>
                </div>
              </div>
            </div>
            
            <div class="section-title">View Modes</div>
            <div class="btn-group">
              <button class="btn primary" id="followMode">Follow Mode</button>
              <button class="btn" id="orbitMode">Orbit Mode</button>
            </div>
            
            <div class="btn-group">
              <button class="btn" id="resetCamera">Reset Camera</button>
              <button class="btn" id="centerView">Center View</button>
            </div>
          </div>
          
          <!-- Settings Tab -->
          <div class="tab-panel" id="settings-tab">
            <div class="section-title">3D Model Selection</div>
            <div class="control-group">
              <div class="model-input-group">
                <label class="control-label">Sketchfab Model ID</label>
                <div class="input-with-button">
                  <input type="text" id="modelId" placeholder="Enter Sketchfab Model ID" class="model-input">
                  <button class="btn primary" id="loadModel">Load Model</button>
                </div>
                <div class="help-text">
                  <i class="fas fa-info-circle"></i>
                  <span>Need help finding a model ID? <a href="#" id="showModelGuide">View Guide</a></span>
                </div>
              </div>

              <div class="preset-models">
                <label class="control-label">Quick Select</label>
                <div class="model-grid">
                  <button class="model-preset" data-id="65df9818a34c44f3a77764d665408f8a" data-name="Fighter Jet">
                    <i class="fas fa-fighter-jet"></i>
                    <span>Fighter Jet</span>
                  </button>
                  <button class="model-preset" data-id="7ce4a93d0e1e4c8b9f5e2a3d4c5b6a7e" data-name="Drone">
                    <i class="fas fa-helicopter"></i>
                    <span>Drone</span>
                  </button>
                  <button class="model-preset" data-id="8df5b04e1f2f5d9c0g6f3b4e5d6c7b8f" data-name="Spacecraft">
                    <i class="fas fa-rocket"></i>
                    <span>Spacecraft</span>
                  </button>
                  <button class="model-preset" data-id="9eg6c15f2g3g6e0d1h7g4c5f6e7d8c9g" data-name="Car">
                    <i class="fas fa-car"></i>
                    <span>Car</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="section-title">Motion Settings</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Filter Alpha</span>
                <div class="slider-container">
                  <input type="range" id="filterAlpha" min="0.90" max="0.99" step="0.01" value="0.98">
                  <span class="slider-value" id="filterAlphaValue">0.98</span>
                </div>
              </div>

              <div class="control-row">
                <span class="control-label">Sensitivity</span>
                <div class="slider-container">
                  <input type="range" id="sensitivity" min="0.5" max="2.0" step="0.1" value="1.0">
                  <span class="slider-value" id="sensitivityValue">1.0</span>
                </div>
              </div>

              <div class="control-row">
                <span class="control-label">Motion Threshold</span>
                <div class="slider-container">
                  <input type="range" id="motionThreshold" min="0.1" max="2.0" step="0.1" value="0.5">
                  <span class="slider-value" id="motionThresholdValue">0.5</span>
                </div>
              </div>
            </div>

            <div class="section-title">Calibration & Reset</div>
            <div class="btn-group">
              <button class="btn warning" id="calibrateSensor">
                <i class="fas fa-crosshairs"></i> Calibrate
              </button>
              <button class="btn success" id="resetOrientation">
                <i class="fas fa-undo"></i> Reset
              </button>
            </div>

            <div class="section-title">Performance</div>
            <div class="control-group">
              <div class="control-row">
                <span class="control-label">Update Rate</span>
                <div class="slider-container">
                  <input type="range" id="updateRate" min="10" max="60" step="5" value="40">
                  <span class="slider-value" id="updateRateValue">40 Hz</span>
                </div>
              </div>

              <div class="toggle-row">
                <span class="control-label">Auto Calibration</span>
                <label class="toggle-switch">
                  <input type="checkbox" id="autoCalibration" checked>
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>

            <div class="section-title">Data Management</div>
            <div class="btn-group">
              <button class="btn" id="exportData">
                <i class="fas fa-download"></i> Export
              </button>
              <button class="btn" id="saveSettings">
                <i class="fas fa-save"></i> Save
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 3D Viewer -->
      <div class="viewer-container">
        <iframe id="api-frame" class="viewer-frame"
          allow="autoplay; fullscreen; xr-spatial-tracking"
          xr-spatial-tracking execution-while-out-of-viewport execution-while-not-rendered web-share
          allowfullscreen mozallowfullscreen="true" webkitallowfullscreen="true">
        </iframe>
        
        <div id="viewerLoading" class="viewer-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading 3D Airplane Model...</div>
        </div>
        
        <div id="viewerOverlay" class="viewer-overlay">
          <div>Camera Mode: <span id="currentMode">Follow</span></div>
          <div>Distance: <span id="currentDistance">8.0</span>m</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // ==================== GLOBAL VARIABLES ====================

    let socket;
    let isConnected = false;
    let api = null;
    let isViewerReady = false;
    let currentMode = 'follow';
    let currentModelId = '65df9818a34c44f3a77764d665408f8a';
    let modelLoadingInProgress = false;

    let cameraSettings = {
      distance: 8,
      height: 2,
      smoothing: 0.1
    };

    let sensorSettings = {
      alpha: 0.98,
      sensitivity: 1.0,
      updateRate: 40,
      motionThreshold: 0.5,
      autoCalibration: true
    };

    // Enhanced Statistics
    let stats = {
      packets: 0,
      startTime: Date.now(),
      lastUpdateTime: 0,
      updateCount: 0,
      fps: 0,
      clientCount: 0,
      motionEvents: 0,
      totalUptime: 0
    };

    // Performance monitoring
    let performanceMonitor = {
      frameCount: 0,
      lastFPSUpdate: 0,
      averageFPS: 0,
      maxFPS: 0,
      minFPS: 999
    };
    
    // Smooth camera transition
    let targetCamera = { x: 0, y: 2, z: 8 };
    let currentCamera = { x: 0, y: 2, z: 8 };
    
    // Tab Management
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const targetTab = button.dataset.tab;
        
        // Update active tab button
        document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
        button.classList.add('active');
        
        // Update active tab panel
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        document.getElementById(targetTab + '-tab').classList.add('active');
      });
    });
    
    // WebSocket Connection with enhanced reconnection
    const initWebSocket = () => {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = protocol + '//' + window.location.hostname + ':81';
      socket = new WebSocket(wsUrl);
      
      socket.onopen = () => {
        console.log('WebSocket connected');
        isConnected = true;
        updateConnectionStatus(true);
        stats.startTime = Date.now();
        stats.packets = 0;
      };
      
      socket.onclose = () => {
        console.log('WebSocket disconnected');
        isConnected = false;
        updateConnectionStatus(false);
        setTimeout(initWebSocket, 3000);
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateConnectionStatus(false);
      };
      
      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          updateSensorDisplay(data);
          updateAirplaneOrientation(data);
          updateStats();
        } catch (error) {
          console.error('Error parsing sensor data:', error);
        }
      };
    };
    
    const updateConnectionStatus = (connected) => {
      const statusEl = document.getElementById('connectionStatus');
      const statusText = statusEl.querySelector('span');
      
      if (connected) {
        statusText.textContent = 'Connected';
        statusEl.className = 'connection-status connected';
      } else {
        statusText.textContent = 'Disconnected';
        statusEl.className = 'connection-status disconnected';
      }
    };
    
    const updateSensorDisplay = (data) => {
      // Orientation data
      document.getElementById('rollValue').textContent = data.roll.toFixed(1);
      document.getElementById('pitchValue').textContent = data.pitch.toFixed(1);
      document.getElementById('yawValue').textContent = data.yaw.toFixed(1);
      
      // Raw sensor data
      if (data.accelX !== undefined) {
        document.getElementById('accelX').textContent = data.accelX.toFixed(2);
        document.getElementById('accelY').textContent = data.accelY.toFixed(2);
        document.getElementById('accelZ').textContent = data.accelZ.toFixed(2);
        document.getElementById('gyroX').textContent = data.gyroX.toFixed(1);
        document.getElementById('gyroY').textContent = data.gyroY.toFixed(1);
        document.getElementById('gyroZ').textContent = data.gyroZ.toFixed(1);
      }
      
      // Temperature
      if (data.temperature !== undefined) {
        document.getElementById('tempValue').textContent = data.temperature.toFixed(1);
      }
    };
    
    const updateStats = () => {
      stats.packets++;
      performanceMonitor.frameCount++;

      const now = Date.now();
      const elapsed = (now - stats.startTime) / 1000;
      const uptimeFormatted = elapsed < 60 ?
        elapsed.toFixed(0) + 's' :
        Math.floor(elapsed / 60) + 'm ' + (elapsed % 60).toFixed(0) + 's';

      // Calculate FPS
      if (now - performanceMonitor.lastFPSUpdate > 1000) {
        const fps = performanceMonitor.frameCount;
        performanceMonitor.averageFPS = (performanceMonitor.averageFPS + fps) / 2;
        performanceMonitor.maxFPS = Math.max(performanceMonitor.maxFPS, fps);
        performanceMonitor.minFPS = Math.min(performanceMonitor.minFPS, fps);

        document.getElementById('fpsDisplay').textContent = fps;
        stats.fps = fps;

        performanceMonitor.frameCount = 0;
        performanceMonitor.lastFPSUpdate = now;
      }

      // Calculate update rate
      if (now - stats.lastUpdateTime > 1000) {
        const rate = stats.updateCount;
        document.getElementById('updateRate').textContent = rate;
        stats.updateCount = 0;
        stats.lastUpdateTime = now;
      } else {
        stats.updateCount++;
      }

      document.getElementById('uptime').textContent = uptimeFormatted;
      document.getElementById('dataPackets').textContent = stats.packets;
    };

    const showModelGuide = () => {
      const guideHTML = `
        <div class="modal-overlay" id="modelGuideModal">
          <div class="modal-content">
            <div class="modal-header">
              <h3><i class="fas fa-book"></i> How to Find Sketchfab Model IDs</h3>
              <button class="modal-close" onclick="closeModal('modelGuideModal')">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="guide-step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4>Visit Sketchfab.com</h4>
                  <p>Go to <a href="https://sketchfab.com" target="_blank">sketchfab.com</a> and search for 3D models</p>
                </div>
              </div>

              <div class="guide-step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4>Choose a Model</h4>
                  <p>Select any 3D model you want to control with your motion sensor</p>
                </div>
              </div>

              <div class="guide-step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4>Copy the Model ID</h4>
                  <p>From the URL like: <code>sketchfab.com/3d-models/airplane-<strong>abc123def456</strong></code></p>
                  <p>Copy the ID part: <strong>abc123def456</strong></p>
                </div>
              </div>

              <div class="guide-step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h4>Load in MotionSync Pro</h4>
                  <p>Paste the ID in the Model ID field and click "Load Model"</p>
                </div>
              </div>

              <div class="guide-tips">
                <h4><i class="fas fa-lightbulb"></i> Pro Tips</h4>
                <ul>
                  <li>Look for models with "Download" enabled for better compatibility</li>
                  <li>Smaller models (under 50MB) load faster</li>
                  <li>Animated models work great with motion control</li>
                  <li>Try searching for: airplanes, cars, robots, spaceships</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', guideHTML);

      // Add modal styles if not already present
      if (!document.getElementById('modalStyles')) {
        const modalStyles = document.createElement('style');
        modalStyles.id = 'modalStyles';
        modalStyles.textContent = `
          .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
          }

          .modal-content {
            background: var(--panel-bg);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
            animation: slideIn 0.3s ease;
          }

          .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px;
            border-bottom: 1px solid var(--border);
          }

          .modal-header h3 {
            color: var(--text-primary);
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .modal-close {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-sm);
            transition: var(--transition);
          }

          .modal-close:hover {
            color: var(--text-primary);
            background: var(--bg-tertiary);
          }

          .modal-body {
            padding: 24px;
          }

          .guide-step {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            align-items: flex-start;
          }

          .step-number {
            width: 32px;
            height: 32px;
            background: var(--accent);
            color: var(--bg-dark);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            flex-shrink: 0;
          }

          .step-content h4 {
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .step-content p {
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 4px;
          }

          .step-content code {
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: var(--accent);
          }

          .step-content a {
            color: var(--accent);
            text-decoration: none;
          }

          .step-content a:hover {
            color: var(--accent-light);
          }

          .guide-tips {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--border-accent);
            border-radius: var(--radius-md);
            padding: 20px;
            margin-top: 24px;
          }

          .guide-tips h4 {
            color: var(--accent);
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .guide-tips ul {
            list-style: none;
            padding: 0;
          }

          .guide-tips li {
            color: var(--text-secondary);
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
          }

          .guide-tips li::before {
            content: '•';
            color: var(--accent);
            position: absolute;
            left: 0;
            font-weight: bold;
          }

          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
          }
        `;
        document.head.appendChild(modalStyles);
      }
    };

    const closeModal = (modalId) => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
          modal.remove();
        }, 300);
      }
    };
    
    // ==================== ENHANCED SKETCHFAB INTEGRATION ====================

    const iframe = document.getElementById('api-frame');
    let client = null;

    const initializeSketchfabViewer = (modelId = currentModelId) => {
      if (modelLoadingInProgress) return;

      modelLoadingInProgress = true;
      isViewerReady = false;

      // Show loading state
      document.getElementById('viewerLoading').style.display = 'flex';
      document.getElementById('viewerLoading').innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading 3D Model...</div>
        <div style="font-size: 12px; color: var(--text-muted); margin-top: 8px;">Model ID: ${modelId}</div>
      `;

      // Clean up previous instance
      if (api) {
        try {
          api.stop();
        } catch (e) {
          console.warn('Error stopping previous API instance:', e);
        }
      }

      // Create new client instance
      client = new Sketchfab(iframe);

      client.init(modelId, {
        ui_controls: 0,
        ui_infos: 0,
        ui_hint: 0,
        ui_animations: 0,
        ui_stop: 0,
        ui_inspector: 0,
        ui_vr: 0,
        ui_help: 0,
        ui_settings: 0,
        ui_fullscreen: 1,
        autostart: 1,
        preload: 1,
        camera: 0,
        success: (apiInstance) => {
          console.log('Sketchfab API initialized successfully');
          api = apiInstance;
          api.start();

          api.addEventListener('viewerready', () => {
            console.log('Sketchfab viewer ready for model:', modelId);
            isViewerReady = true;
            modelLoadingInProgress = false;
            currentModelId = modelId;

            document.getElementById('viewerLoading').style.display = 'none';
            document.getElementById('viewerOverlay').classList.add('show');

            // Configure viewer
            api.setUserInteraction(currentMode === 'orbit');
            resetCamera();
            startCameraAnimation();

            // Update model input field
            document.getElementById('modelId').value = modelId;
            updateActiveModelPreset(modelId);

            showNotification('3D Model loaded successfully!', 'success');

            // Save model ID to device
            sendCommand('setModelId', modelId);
          });

          api.addEventListener('error', (error) => {
            console.error('Sketchfab viewer error:', error);
            handleModelLoadError();
          });
        },
        error: (error) => {
          console.error('Sketchfab initialization error:', error);
          handleModelLoadError();
        }
      });
    };

    const handleModelLoadError = () => {
      modelLoadingInProgress = false;
      document.getElementById('viewerLoading').innerHTML = `
        <div class="loading-text" style="color: var(--error);">
          <i class="fas fa-exclamation-triangle"></i>
          Error loading 3D model
        </div>
        <div style="font-size: 12px; color: var(--text-muted); margin-top: 8px;">
          Please check the model ID and try again
        </div>
      `;
      showNotification('Failed to load 3D model. Please check the model ID.', 'error');
    };

    const loadNewModel = (modelId) => {
      if (!modelId || modelId.trim() === '') {
        showNotification('Please enter a valid model ID', 'warning');
        return;
      }

      if (modelId === currentModelId) {
        showNotification('This model is already loaded', 'info');
        return;
      }

      console.log('Loading new model:', modelId);
      initializeSketchfabViewer(modelId.trim());
    };

    const updateActiveModelPreset = (modelId) => {
      document.querySelectorAll('.model-preset').forEach(preset => {
        preset.classList.remove('active');
        if (preset.dataset.id === modelId) {
          preset.classList.add('active');
        }
      });
    };
    
    // Enhanced math functions
    const deg2rad = (degrees) => degrees * Math.PI / 180;
    const rad2deg = (radians) => radians * 180 / Math.PI;
    const clamp = (value, min, max) => Math.max(min, Math.min(max, value));
    const lerp = (a, b, t) => a + (b - a) * t;
    
    // Enhanced camera functions
    const resetCamera = () => {
      if (!api || !isViewerReady) return;
      
      targetCamera = { x: 0, y: cameraSettings.height, z: cameraSettings.distance };
      currentCamera = { ...targetCamera };
      
      const eye = [currentCamera.x, currentCamera.y, currentCamera.z];
      const target = [0, 0, 0];
      
      api.setCameraLookAt(eye, target, 0, (err) => {
        if (err) console.warn('setCameraLookAt error:', err);
      });
      
      updateOverlay();
    };
    
    const updateAirplaneOrientation = (data) => {
      if (!api || !isViewerReady) return;
      
      if (currentMode === 'follow') {
        const yawDeg = -data.yaw * sensorSettings.sensitivity;
        const pitchDeg = clamp(-data.pitch * sensorSettings.sensitivity, -85, 85);
        const rollDeg = data.roll * sensorSettings.sensitivity; // For future roll implementation
        
        const yaw = deg2rad(yawDeg);
        const pitch = deg2rad(pitchDeg);
        
        // Calculate target camera position
        const distance = cameraSettings.distance;
        const height = cameraSettings.height;
        
        targetCamera.x = distance * Math.cos(pitch) * Math.sin(yaw);
        targetCamera.y = distance * Math.sin(pitch) + height;
        targetCamera.z = distance * Math.cos(pitch) * Math.cos(yaw);
      }
    };
    
    // Smooth camera animation loop
    const startCameraAnimation = () => {
      const animate = () => {
        if (!api || !isViewerReady) {
          requestAnimationFrame(animate);
          return;
        }
        
        // Smooth camera interpolation
        const smoothing = cameraSettings.smoothing;
        currentCamera.x = lerp(currentCamera.x, targetCamera.x, smoothing);
        currentCamera.y = lerp(currentCamera.y, targetCamera.y, smoothing);
        currentCamera.z = lerp(currentCamera.z, targetCamera.z, smoothing);
        
        const eye = [currentCamera.x, currentCamera.y, currentCamera.z];
        const target = [0, 0, 0];
        
        api.setCameraLookAt(eye, target, 0, (err) => {
          if (err) console.warn('setCameraLookAt error:', err);
        });
        
        requestAnimationFrame(animate);
      };
      
      animate();
    };
    
    const updateOverlay = () => {
      document.getElementById('currentMode').textContent = currentMode === 'follow' ? 'Follow' : 'Orbit';
      document.getElementById('currentDistance').textContent = cameraSettings.distance.toFixed(1);
    };
    
    // ==================== ENHANCED CONTROL SETUP ====================

    const setupControls = () => {
      // Model Selection Controls
      const modelIdInput = document.getElementById('modelId');
      const loadModelBtn = document.getElementById('loadModel');
      const showGuideBtn = document.getElementById('showModelGuide');

      loadModelBtn.addEventListener('click', () => {
        const modelId = modelIdInput.value.trim();
        loadNewModel(modelId);
      });

      modelIdInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const modelId = modelIdInput.value.trim();
          loadNewModel(modelId);
        }
      });

      showGuideBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showModelGuide();
      });

      // Model Preset Buttons
      document.querySelectorAll('.model-preset').forEach(preset => {
        preset.addEventListener('click', () => {
          const modelId = preset.dataset.id;
          const modelName = preset.dataset.name;
          loadNewModel(modelId);
          showNotification(`Loading ${modelName}...`, 'info');
        });
      });

      // Camera controls
      const distanceSlider = document.getElementById('cameraDistance');
      const heightSlider = document.getElementById('cameraHeight');
      const smoothingSlider = document.getElementById('cameraSmoothing');

      distanceSlider.addEventListener('input', (e) => {
        cameraSettings.distance = parseFloat(e.target.value);
        document.getElementById('cameraDistanceValue').textContent = cameraSettings.distance.toFixed(1);
        updateOverlay();
      });

      heightSlider.addEventListener('input', (e) => {
        cameraSettings.height = parseFloat(e.target.value);
        document.getElementById('cameraHeightValue').textContent = cameraSettings.height.toFixed(1);
      });

      smoothingSlider.addEventListener('input', (e) => {
        cameraSettings.smoothing = parseFloat(e.target.value);
        document.getElementById('cameraSmoothingValue').textContent = cameraSettings.smoothing.toFixed(2);
      });
      
      // Enhanced Sensor settings
      const alphaSlider = document.getElementById('filterAlpha');
      const sensitivitySlider = document.getElementById('sensitivity');
      const updateRateSlider = document.getElementById('updateRate');
      const motionThresholdSlider = document.getElementById('motionThreshold');
      const autoCalibrationToggle = document.getElementById('autoCalibration');

      alphaSlider.addEventListener('input', (e) => {
        sensorSettings.alpha = parseFloat(e.target.value);
        document.getElementById('filterAlphaValue').textContent = sensorSettings.alpha.toFixed(2);
        sendCommand('setAlpha', sensorSettings.alpha);
      });

      sensitivitySlider.addEventListener('input', (e) => {
        sensorSettings.sensitivity = parseFloat(e.target.value);
        document.getElementById('sensitivityValue').textContent = sensorSettings.sensitivity.toFixed(1);
        sendCommand('setSensitivity', sensorSettings.sensitivity);
      });

      updateRateSlider.addEventListener('input', (e) => {
        sensorSettings.updateRate = parseInt(e.target.value);
        document.getElementById('updateRateValue').textContent = sensorSettings.updateRate + ' Hz';
        sendCommand('setUpdateRate', sensorSettings.updateRate);
      });

      motionThresholdSlider.addEventListener('input', (e) => {
        sensorSettings.motionThreshold = parseFloat(e.target.value);
        document.getElementById('motionThresholdValue').textContent = sensorSettings.motionThreshold.toFixed(1);
        sendCommand('setMotionThreshold', sensorSettings.motionThreshold);
      });

      autoCalibrationToggle.addEventListener('change', (e) => {
        sensorSettings.autoCalibration = e.target.checked;
        sendCommand('setAutoCalibration', sensorSettings.autoCalibration);
        showNotification(`Auto calibration ${e.target.checked ? 'enabled' : 'disabled'}`, 'info');
      });
      
      // Mode buttons
      document.getElementById('followMode').addEventListener('click', () => {
        currentMode = 'follow';
        api.setUserInteraction(false);
        updateModeButtons();
        updateOverlay();
      });
      
      document.getElementById('orbitMode').addEventListener('click', () => {
        currentMode = 'orbit';
        api.setUserInteraction(true);
        updateModeButtons();
        updateOverlay();
      });
      
      // Enhanced Action buttons
      document.getElementById('resetCamera').addEventListener('click', resetCamera);
      document.getElementById('centerView').addEventListener('click', resetCamera);

      document.getElementById('calibrateSensor').addEventListener('click', () => {
        sendCommand('calibrate');
        showNotification('Calibrating sensor... Keep device steady for 3 seconds', 'warning');
      });

      document.getElementById('resetOrientation').addEventListener('click', () => {
        sendCommand('resetOrientation');
        showNotification('Orientation reset', 'success');
      });

      document.getElementById('exportData').addEventListener('click', exportSensorData);

      document.getElementById('saveSettings').addEventListener('click', () => {
        saveAllSettings();
        showNotification('Settings saved successfully', 'success');
      });
    };

    // ==================== ENHANCED UTILITY FUNCTIONS ====================

    const saveAllSettings = () => {
      const settings = {
        camera: cameraSettings,
        sensor: sensorSettings,
        modelId: currentModelId,
        mode: currentMode
      };

      sendCommand('saveSettings', settings);

      // Also save to localStorage as backup
      try {
        localStorage.setItem('motionSyncSettings', JSON.stringify(settings));
      } catch (e) {
        console.warn('Could not save to localStorage:', e);
      }
    };

    const loadSavedSettings = () => {
      try {
        const saved = localStorage.getItem('motionSyncSettings');
        if (saved) {
          const settings = JSON.parse(saved);

          // Apply camera settings
          if (settings.camera) {
            Object.assign(cameraSettings, settings.camera);
            updateCameraSliders();
          }

          // Apply sensor settings
          if (settings.sensor) {
            Object.assign(sensorSettings, settings.sensor);
            updateSensorSliders();
          }

          // Apply model ID
          if (settings.modelId && settings.modelId !== currentModelId) {
            currentModelId = settings.modelId;
            document.getElementById('modelId').value = currentModelId;
          }

          // Apply mode
          if (settings.mode) {
            currentMode = settings.mode;
          }
        }
      } catch (e) {
        console.warn('Could not load from localStorage:', e);
      }
    };

    const updateCameraSliders = () => {
      document.getElementById('cameraDistance').value = cameraSettings.distance;
      document.getElementById('cameraDistanceValue').textContent = cameraSettings.distance.toFixed(1);
      document.getElementById('cameraHeight').value = cameraSettings.height;
      document.getElementById('cameraHeightValue').textContent = cameraSettings.height.toFixed(1);
      document.getElementById('cameraSmoothing').value = cameraSettings.smoothing;
      document.getElementById('cameraSmoothingValue').textContent = cameraSettings.smoothing.toFixed(2);
    };

    const updateSensorSliders = () => {
      document.getElementById('filterAlpha').value = sensorSettings.alpha;
      document.getElementById('filterAlphaValue').textContent = sensorSettings.alpha.toFixed(2);
      document.getElementById('sensitivity').value = sensorSettings.sensitivity;
      document.getElementById('sensitivityValue').textContent = sensorSettings.sensitivity.toFixed(1);
      document.getElementById('updateRate').value = sensorSettings.updateRate;
      document.getElementById('updateRateValue').textContent = sensorSettings.updateRate + ' Hz';
      document.getElementById('motionThreshold').value = sensorSettings.motionThreshold;
      document.getElementById('motionThresholdValue').textContent = sensorSettings.motionThreshold.toFixed(1);
      document.getElementById('autoCalibration').checked = sensorSettings.autoCalibration;
    };
    
    const updateModeButtons = () => {
      const followBtn = document.getElementById('followMode');
      const orbitBtn = document.getElementById('orbitMode');
      
      if (currentMode === 'follow') {
        followBtn.classList.add('primary');
        orbitBtn.classList.remove('primary');
      } else {
        orbitBtn.classList.add('primary');
        followBtn.classList.remove('primary');
      }
    };
    
    const sendCommand = (command, value = null) => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const message = { command, value };
        socket.send(JSON.stringify(message));
      }
    };
    
    const showNotification = (message, type = 'info') => {
      // Create notification element
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 24px;
        background: var(--panel-bg);
        backdrop-filter: blur(24px);
        border: 1px solid var(--border);
        border-radius: var(--radius-md);
        padding: 16px 20px;
        color: var(--text-primary);
        font-size: 14px;
        font-weight: 600;
        z-index: 1000;
        transform: translateX(100%);
        transition: var(--transition);
        max-width: 300px;
        box-shadow: var(--shadow-lg);
      `;
      
      if (type === 'success') {
        notification.style.borderColor = 'rgba(0, 255, 136, 0.3)';
        notification.style.background = 'rgba(0, 255, 136, 0.1)';
      } else if (type === 'warning') {
        notification.style.borderColor = 'rgba(255, 170, 0, 0.3)';
        notification.style.background = 'rgba(255, 170, 0, 0.1)';
      } else if (type === 'error') {
        notification.style.borderColor = 'rgba(255, 71, 87, 0.3)';
        notification.style.background = 'rgba(255, 71, 87, 0.1)';
      }
      
      notification.textContent = message;
      document.body.appendChild(notification);
      
      // Animate in
      setTimeout(() => {
        notification.style.transform = 'translateX(0)';
      }, 100);
      
      // Remove after 3 seconds
      setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    };
    
    const exportSensorData = () => {
      const data = {
        timestamp: new Date().toISOString(),
        stats: stats,
        settings: { ...cameraSettings, ...sensorSettings },
        currentOrientation: {
          roll: parseFloat(document.getElementById('rollValue').textContent),
          pitch: parseFloat(document.getElementById('pitchValue').textContent),
          yaw: parseFloat(document.getElementById('yawValue').textContent)
        }
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mpu6050-data-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      showNotification('Data exported successfully', 'success');
    };
    
    const toggleStatsDisplay = () => {
      const statsGrid = document.querySelector('.stats-grid');
      const isVisible = statsGrid.style.display !== 'none';
      statsGrid.style.display = isVisible ? 'none' : 'grid';
      document.getElementById('toggleStats').textContent = isVisible ? 'Show Stats' : 'Hide Stats';
    };
    
    // ==================== INITIALIZATION ====================

    window.addEventListener('load', () => {
      console.log('MotionSync Pro 3D - Initializing...');

      // Load saved settings first
      loadSavedSettings();

      // Initialize WebSocket connection
      initWebSocket();

      // Setup all controls
      setupControls();
      updateModeButtons();

      // Initialize Sketchfab viewer with current model
      initializeSketchfabViewer(currentModelId);

      // Set initial slider values
      updateCameraSliders();
      updateSensorSliders();

      // Initialize performance monitoring
      performanceMonitor.lastFPSUpdate = Date.now();

      console.log('MotionSync Pro 3D - Ready!');

      // Show welcome notification
      setTimeout(() => {
        showNotification('Welcome to MotionSync Pro 3D! 🎮', 'success');
      }, 1000);
    });
    
    // Reconnection on page focus
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !isConnected) {
        initWebSocket();
      }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'r' || e.key === 'R') {
        resetCamera();
      } else if (e.key === 'c' || e.key === 'C') {
        sendCommand('calibrate');
      } else if (e.key === ' ') {
        e.preventDefault();
        document.getElementById(currentMode === 'follow' ? 'orbitMode' : 'followMode').click();
      }
    });
  </script>
</body>
</html>