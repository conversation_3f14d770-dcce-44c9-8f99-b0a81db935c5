#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeMonoOblique18pt7bBitmaps[] PROGMEM = {
    0x00, 0x1C, 0x38, 0x70, 0xC1, 0x83, 0x06, 0x18, 0x30, 0x60, 0xC1, 0x02,
    0x04, 0x00, 0x00, 0x01, 0xC7, 0x8F, 0x1C, 0x00, 0x78, 0x7B, 0xC3, 0xFC,
    0x3D, 0xE1, 0xEF, 0x0F, 0x70, 0x73, 0x83, 0x98, 0x18, 0xC0, 0xC6, 0x06,
    0x00, 0x00, 0x8C, 0x01, 0x18, 0x06, 0x20, 0x08, 0x40, 0x11, 0x80, 0x62,
    0x00, 0xC4, 0x01, 0x18, 0x02, 0x30, 0x7F, 0xFC, 0x10, 0x80, 0x23, 0x00,
    0xC4, 0x01, 0x88, 0x3F, 0xFF, 0x04, 0x60, 0x18, 0x80, 0x21, 0x00, 0x46,
    0x01, 0x88, 0x03, 0x10, 0x04, 0x60, 0x08, 0xC0, 0x31, 0x00, 0x00, 0x30,
    0x00, 0x20, 0x00, 0x20, 0x00, 0xF9, 0x03, 0x0F, 0x06, 0x03, 0x04, 0x03,
    0x08, 0x00, 0x08, 0x00, 0x08, 0x00, 0x04, 0x00, 0x03, 0xC0, 0x00, 0x78,
    0x00, 0x0C, 0x00, 0x04, 0x00, 0x04, 0x40, 0x04, 0x40, 0x08, 0x40, 0x18,
    0xF0, 0x60, 0x9F, 0x80, 0x02, 0x00, 0x06, 0x00, 0x04, 0x00, 0x04, 0x00,
    0x04, 0x00, 0x03, 0xC0, 0x0C, 0x60, 0x08, 0x20, 0x10, 0x20, 0x10, 0x20,
    0x10, 0x40, 0x18, 0x80, 0x0F, 0x00, 0x00, 0x0F, 0x00, 0x78, 0x07, 0xC0,
    0x3C, 0x00, 0xE0, 0x00, 0x01, 0xE0, 0x02, 0x18, 0x04, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x08, 0x10, 0x0C, 0x20, 0x07, 0xC0, 0x01, 0xF0, 0x11, 0x81,
    0x00, 0x10, 0x00, 0x80, 0x04, 0x00, 0x20, 0x01, 0x80, 0x04, 0x00, 0xF0,
    0x09, 0x86, 0x84, 0x48, 0x32, 0x40, 0xA2, 0x07, 0x10, 0x30, 0x43, 0x81,
    0xE7, 0x80, 0x7B, 0xFD, 0xEF, 0x73, 0x98, 0xC6, 0x00, 0x01, 0x02, 0x06,
    0x0C, 0x0C, 0x18, 0x10, 0x30, 0x30, 0x60, 0x60, 0x60, 0xC0, 0xC0, 0xC0,
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0x40, 0x60, 0x60, 0x20, 0x04, 0x06,
    0x06, 0x02, 0x02, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x06,
    0x06, 0x06, 0x0C, 0x0C, 0x0C, 0x18, 0x10, 0x30, 0x60, 0x40, 0xC0, 0x01,
    0x00, 0x04, 0x00, 0x10, 0x00, 0xC6, 0xE3, 0xF8, 0x7E, 0x00, 0x70, 0x03,
    0x40, 0x19, 0x80, 0xC2, 0x06, 0x0C, 0x00, 0x00, 0xC0, 0x01, 0x00, 0x02,
    0x00, 0x04, 0x00, 0x08, 0x00, 0x20, 0x00, 0x40, 0x00, 0x80, 0xFF, 0xFE,
    0x02, 0x00, 0x08, 0x00, 0x10, 0x00, 0x20, 0x00, 0x40, 0x00, 0x80, 0x02,
    0x00, 0x04, 0x00, 0x0F, 0x87, 0x87, 0x83, 0x83, 0xC1, 0xC1, 0xC0, 0xC0,
    0xE0, 0x60, 0x00, 0xFF, 0xFF, 0x77, 0xFF, 0xF7, 0x00, 0x00, 0x00, 0x60,
    0x00, 0x08, 0x00, 0x02, 0x00, 0x00, 0xC0, 0x00, 0x30, 0x00, 0x04, 0x00,
    0x01, 0x80, 0x00, 0x60, 0x00, 0x08, 0x00, 0x03, 0x00, 0x00, 0xC0, 0x00,
    0x10, 0x00, 0x06, 0x00, 0x01, 0x80, 0x00, 0x20, 0x00, 0x0C, 0x00, 0x03,
    0x00, 0x00, 0x40, 0x00, 0x18, 0x00, 0x06, 0x00, 0x00, 0x80, 0x00, 0x20,
    0x00, 0x0C, 0x00, 0x03, 0x00, 0x00, 0x40, 0x00, 0x08, 0x00, 0x00, 0x01,
    0xF0, 0x18, 0x60, 0x80, 0x86, 0x01, 0x10, 0x04, 0x80, 0x12, 0x00, 0x50,
    0x01, 0x40, 0x0D, 0x00, 0x24, 0x00, 0xA0, 0x02, 0x80, 0x1A, 0x00, 0x48,
    0x01, 0x20, 0x0C, 0x80, 0x22, 0x01, 0x84, 0x0C, 0x18, 0x60, 0x3E, 0x00,
    0x00, 0x60, 0x07, 0x00, 0x68, 0x06, 0x40, 0xE4, 0x04, 0x20, 0x01, 0x00,
    0x08, 0x00, 0x40, 0x04, 0x00, 0x20, 0x01, 0x00, 0x08, 0x00, 0x80, 0x04,
    0x00, 0x20, 0x01, 0x00, 0x08, 0x00, 0x80, 0x04, 0x0F, 0xFF, 0x80, 0x00,
    0x3C, 0x00, 0x61, 0x80, 0x40, 0x40, 0x40, 0x10, 0x60, 0x08, 0x00, 0x04,
    0x00, 0x02, 0x00, 0x02, 0x00, 0x03, 0x00, 0x03, 0x00, 0x07, 0x00, 0x07,
    0x00, 0x06, 0x00, 0x06, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x0C, 0x00, 0x0C,
    0x00, 0x1C, 0x01, 0x1C, 0x00, 0x8F, 0xFF, 0xC0, 0x00, 0xFC, 0x03, 0x06,
    0x06, 0x03, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x02, 0x00, 0x02,
    0x00, 0x0C, 0x00, 0xF0, 0x00, 0x18, 0x00, 0x04, 0x00, 0x02, 0x00, 0x02,
    0x00, 0x02, 0x00, 0x02, 0x00, 0x04, 0x00, 0x04, 0x40, 0x18, 0x70, 0x30,
    0x0F, 0xC0, 0x00, 0x1C, 0x00, 0xD0, 0x06, 0x80, 0x32, 0x00, 0x88, 0x04,
    0x20, 0x30, 0x81, 0x84, 0x04, 0x10, 0x20, 0x41, 0x81, 0x0C, 0x08, 0x60,
    0x21, 0x00, 0x8F, 0xFF, 0x80, 0x18, 0x00, 0x40, 0x01, 0x00, 0x04, 0x00,
    0x10, 0x07, 0xE0, 0x03, 0xFF, 0x03, 0x00, 0x01, 0x80, 0x00, 0x80, 0x00,
    0x40, 0x00, 0x20, 0x00, 0x30, 0x00, 0x1B, 0xE0, 0x0E, 0x0C, 0x00, 0x02,
    0x00, 0x00, 0x80, 0x00, 0x40, 0x00, 0x20, 0x00, 0x10, 0x00, 0x08, 0x00,
    0x08, 0x00, 0x04, 0x60, 0x04, 0x18, 0x04, 0x06, 0x0C, 0x00, 0xF8, 0x00,
    0x00, 0x3F, 0x00, 0xC0, 0x03, 0x00, 0x04, 0x00, 0x08, 0x00, 0x10, 0x00,
    0x30, 0x00, 0x20, 0x00, 0x40, 0x00, 0x43, 0xE0, 0x4C, 0x30, 0xB0, 0x18,
    0xE0, 0x08, 0xC0, 0x08, 0x80, 0x08, 0x80, 0x08, 0x80, 0x10, 0xC0, 0x10,
    0x40, 0x20, 0x20, 0xC0, 0x1F, 0x00, 0xFF, 0xFC, 0x00, 0xE0, 0x04, 0x00,
    0x60, 0x02, 0x00, 0x30, 0x01, 0x00, 0x18, 0x00, 0x80, 0x0C, 0x00, 0x40,
    0x06, 0x00, 0x20, 0x03, 0x00, 0x10, 0x01, 0x80, 0x08, 0x00, 0xC0, 0x04,
    0x00, 0x60, 0x02, 0x00, 0x00, 0x00, 0xF0, 0x06, 0x18, 0x10, 0x18, 0x40,
    0x11, 0x00, 0x22, 0x00, 0x44, 0x00, 0x88, 0x02, 0x18, 0x08, 0x18, 0x60,
    0x1F, 0x80, 0xC1, 0x82, 0x01, 0x88, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80,
    0x09, 0x00, 0x23, 0x00, 0x83, 0x06, 0x01, 0xF0, 0x00, 0x00, 0xF0, 0x06,
    0x18, 0x10, 0x10, 0x40, 0x30, 0x80, 0x22, 0x00, 0x44, 0x00, 0x88, 0x03,
    0x10, 0x0E, 0x30, 0x34, 0x30, 0xD0, 0x3E, 0x20, 0x00, 0x40, 0x01, 0x00,
    0x02, 0x00, 0x08, 0x00, 0x20, 0x00, 0xC0, 0x02, 0x00, 0x18, 0x0F, 0xC0,
    0x00, 0x1C, 0x7C, 0xF9, 0xF1, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xC7, 0xCF,
    0x9F, 0x1C, 0x00, 0x01, 0xC0, 0x7C, 0x0F, 0x81, 0xF0, 0x1C, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x07, 0x81, 0xE0, 0x3C, 0x0F, 0x01,
    0xC0, 0x70, 0x0E, 0x03, 0x80, 0x60, 0x00, 0x00, 0x01, 0x80, 0x03, 0x80,
    0x07, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x00,
    0xE0, 0x00, 0x1C, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x38, 0x00, 0x07,
    0x00, 0x00, 0xE0, 0x00, 0x38, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x18, 0x00, 0x03, 0x80,
    0x00, 0x38, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x0E, 0x00, 0x00, 0xE0,
    0x00, 0x0E, 0x00, 0x03, 0x80, 0x03, 0x80, 0x03, 0x80, 0x03, 0x80, 0x03,
    0x80, 0x03, 0x80, 0x03, 0x80, 0x03, 0x80, 0x00, 0x1F, 0xCE, 0x06, 0x80,
    0x38, 0x01, 0x80, 0x10, 0x01, 0x00, 0x20, 0x04, 0x01, 0x80, 0xF0, 0x18,
    0x01, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x0F, 0x80, 0xF8,
    0x07, 0x00, 0x01, 0xF0, 0x0C, 0x30, 0x30, 0x30, 0x40, 0x21, 0x00, 0x44,
    0x00, 0x88, 0x01, 0x10, 0x1E, 0x40, 0xC4, 0x86, 0x11, 0x08, 0x22, 0x20,
    0x48, 0x40, 0x90, 0x82, 0x21, 0x84, 0x40, 0xFC, 0x80, 0x01, 0x00, 0x02,
    0x00, 0x04, 0x00, 0x04, 0x00, 0x0C, 0x18, 0x07, 0xC0, 0x00, 0x01, 0xFE,
    0x00, 0x00, 0x68, 0x00, 0x06, 0x40, 0x00, 0x32, 0x00, 0x03, 0x10, 0x00,
    0x10, 0x80, 0x01, 0x84, 0x00, 0x18, 0x10, 0x00, 0xC0, 0x80, 0x0C, 0x04,
    0x00, 0x60, 0x20, 0x06, 0x01, 0x00, 0x3F, 0xFC, 0x02, 0x00, 0x20, 0x10,
    0x01, 0x01, 0x00, 0x08, 0x08, 0x00, 0x40, 0x80, 0x02, 0x0C, 0x00, 0x09,
    0xFC, 0x07, 0xF0, 0x0F, 0xFF, 0x00, 0x40, 0x60, 0x20, 0x0C, 0x08, 0x01,
    0x02, 0x00, 0x40, 0x80, 0x10, 0x40, 0x08, 0x10, 0x06, 0x04, 0x03, 0x01,
    0xFF, 0x80, 0x40, 0x38, 0x20, 0x02, 0x08, 0x00, 0x42, 0x00, 0x10, 0x80,
    0x04, 0x40, 0x01, 0x10, 0x00, 0x84, 0x00, 0x41, 0x00, 0x23, 0xFF, 0xF0,
    0x00, 0xFC, 0x40, 0xC1, 0xF0, 0xC0, 0x1C, 0x60, 0x06, 0x10, 0x00, 0x88,
    0x00, 0x24, 0x00, 0x01, 0x00, 0x00, 0x40, 0x00, 0x30, 0x00, 0x08, 0x00,
    0x02, 0x00, 0x00, 0x80, 0x00, 0x20, 0x00, 0x08, 0x00, 0x03, 0x00, 0x00,
    0x40, 0x06, 0x08, 0x03, 0x01, 0x83, 0x80, 0x3F, 0x00, 0x0F, 0xFE, 0x00,
    0x80, 0xC0, 0x20, 0x18, 0x10, 0x02, 0x04, 0x00, 0x41, 0x00, 0x10, 0x40,
    0x04, 0x20, 0x01, 0x08, 0x00, 0x42, 0x00, 0x10, 0x80, 0x08, 0x20, 0x02,
    0x10, 0x00, 0x84, 0x00, 0x21, 0x00, 0x10, 0x40, 0x08, 0x20, 0x06, 0x08,
    0x03, 0x02, 0x01, 0x83, 0xFF, 0x80, 0x0F, 0xFF, 0xE0, 0x10, 0x02, 0x02,
    0x00, 0x60, 0x20, 0x06, 0x02, 0x00, 0x60, 0x20, 0x00, 0x04, 0x00, 0x00,
    0x40, 0x80, 0x04, 0x10, 0x00, 0x7F, 0x00, 0x04, 0x10, 0x00, 0x81, 0x00,
    0x08, 0x00, 0x00, 0x80, 0x00, 0x08, 0x00, 0x81, 0x00, 0x08, 0x10, 0x00,
    0x81, 0x00, 0x18, 0x10, 0x01, 0x8F, 0xFF, 0xF0, 0x0F, 0xFF, 0xF0, 0x10,
    0x03, 0x02, 0x00, 0x30, 0x20, 0x03, 0x02, 0x00, 0x20, 0x20, 0x00, 0x04,
    0x00, 0x00, 0x40, 0x80, 0x04, 0x10, 0x00, 0x7F, 0x00, 0x04, 0x10, 0x00,
    0x81, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x08, 0x00, 0x01, 0x00, 0x00,
    0x10, 0x00, 0x01, 0x00, 0x00, 0x10, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0xFE,
    0x40, 0xC0, 0xF0, 0x40, 0x1C, 0x20, 0x03, 0x10, 0x00, 0x88, 0x00, 0x02,
    0x00, 0x01, 0x00, 0x00, 0x40, 0x00, 0x10, 0x00, 0x08, 0x00, 0x02, 0x01,
    0xFE, 0x80, 0x02, 0x20, 0x00, 0x88, 0x00, 0x22, 0x00, 0x08, 0x40, 0x04,
    0x18, 0x01, 0x03, 0x81, 0xC0, 0x3F, 0x80, 0x07, 0xE1, 0xF8, 0x08, 0x02,
    0x00, 0x80, 0x10, 0x04, 0x00, 0x80, 0x20, 0x04, 0x01, 0x00, 0x20, 0x18,
    0x02, 0x00, 0x80, 0x10, 0x04, 0x00, 0x80, 0x3F, 0xFC, 0x01, 0x00, 0x60,
    0x10, 0x02, 0x00, 0x80, 0x10, 0x04, 0x00, 0x80, 0x20, 0x04, 0x02, 0x00,
    0x40, 0x10, 0x02, 0x00, 0x80, 0x10, 0x04, 0x00, 0x81, 0xF8, 0x3F, 0x00,
    0x0F, 0xFF, 0x80, 0x10, 0x00, 0x08, 0x00, 0x08, 0x00, 0x04, 0x00, 0x02,
    0x00, 0x01, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x40, 0x00, 0x20, 0x00,
    0x10, 0x00, 0x08, 0x00, 0x08, 0x00, 0x04, 0x00, 0x02, 0x00, 0x01, 0x00,
    0x01, 0x00, 0x00, 0x80, 0x1F, 0xFF, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x20,
    0x00, 0x02, 0x00, 0x00, 0x20, 0x00, 0x02, 0x00, 0x00, 0x20, 0x00, 0x04,
    0x00, 0x00, 0x40, 0x00, 0x04, 0x00, 0x00, 0x40, 0x00, 0x0C, 0x04, 0x00,
    0x80, 0x40, 0x08, 0x08, 0x00, 0x80, 0x80, 0x08, 0x08, 0x01, 0x00, 0x80,
    0x10, 0x0C, 0x02, 0x00, 0x60, 0xC0, 0x01, 0xF0, 0x00, 0x0F, 0xE1, 0xF8,
    0x08, 0x03, 0x00, 0x80, 0x60, 0x04, 0x06, 0x00, 0x20, 0x60, 0x01, 0x06,
    0x00, 0x10, 0xC0, 0x00, 0x8C, 0x00, 0x04, 0xC0, 0x00, 0x2F, 0x80, 0x01,
    0x8E, 0x00, 0x18, 0x30, 0x00, 0x80, 0xC0, 0x04, 0x06, 0x00, 0x20, 0x10,
    0x02, 0x00, 0xC0, 0x10, 0x06, 0x00, 0x80, 0x30, 0x04, 0x00, 0x81, 0xFC,
    0x07, 0x80, 0x07, 0xFC, 0x00, 0x10, 0x00, 0x08, 0x00, 0x02, 0x00, 0x00,
    0x80, 0x00, 0x20, 0x00, 0x08, 0x00, 0x04, 0x00, 0x01, 0x00, 0x00, 0x40,
    0x00, 0x10, 0x00, 0x08, 0x00, 0x02, 0x00, 0x00, 0x80, 0x10, 0x20, 0x04,
    0x08, 0x01, 0x04, 0x00, 0x81, 0x00, 0x20, 0x40, 0x0B, 0xFF, 0xFE, 0x0F,
    0x00, 0x1E, 0x03, 0x00, 0x38, 0x05, 0x00, 0x68, 0x04, 0x80, 0x68, 0x04,
    0x80, 0xC8, 0x04, 0x80, 0x90, 0x04, 0x81, 0x90, 0x08, 0x43, 0x10, 0x08,
    0x42, 0x10, 0x08, 0x46, 0x10, 0x08, 0x4C, 0x20, 0x10, 0x2C, 0x20, 0x10,
    0x38, 0x20, 0x10, 0x30, 0x20, 0x10, 0x00, 0x40, 0x10, 0x00, 0x40, 0x20,
    0x00, 0x40, 0x20, 0x00, 0x40, 0x20, 0x00, 0x40, 0xFC, 0x07, 0xE0, 0x1F,
    0x01, 0xFC, 0x0C, 0x00, 0x80, 0x78, 0x02, 0x01, 0xE0, 0x18, 0x04, 0x80,
    0x60, 0x13, 0x01, 0x00, 0x4C, 0x04, 0x03, 0x18, 0x10, 0x0C, 0x60, 0xC0,
    0x20, 0x83, 0x00, 0x83, 0x08, 0x06, 0x0C, 0x20, 0x18, 0x18, 0x80, 0x40,
    0x66, 0x01, 0x00, 0x98, 0x04, 0x03, 0x40, 0x30, 0x0D, 0x00, 0xC0, 0x14,
    0x02, 0x00, 0x70, 0x3F, 0x80, 0xC0, 0x00, 0xF8, 0x01, 0x83, 0x01, 0x00,
    0xC1, 0x00, 0x21, 0x00, 0x19, 0x00, 0x04, 0x80, 0x02, 0x80, 0x01, 0x40,
    0x00, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x28, 0x00, 0x14, 0x00, 0x12,
    0x00, 0x09, 0x80, 0x08, 0x40, 0x08, 0x30, 0x08, 0x0C, 0x18, 0x01, 0xF0,
    0x00, 0x0F, 0xFE, 0x00, 0x40, 0x60, 0x20, 0x0C, 0x08, 0x01, 0x02, 0x00,
    0x40, 0x80, 0x10, 0x40, 0x04, 0x10, 0x02, 0x04, 0x01, 0x01, 0x01, 0x80,
    0x7F, 0x80, 0x20, 0x00, 0x08, 0x00, 0x02, 0x00, 0x00, 0x80, 0x00, 0x40,
    0x00, 0x10, 0x00, 0x04, 0x00, 0x01, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xF8,
    0x01, 0x83, 0x01, 0x00, 0xC1, 0x00, 0x21, 0x00, 0x19, 0x00, 0x05, 0x00,
    0x02, 0x80, 0x01, 0x40, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x28,
    0x00, 0x14, 0x00, 0x12, 0x00, 0x09, 0x80, 0x08, 0x40, 0x08, 0x30, 0x08,
    0x0C, 0x18, 0x03, 0xF0, 0x00, 0xC0, 0x01, 0xC0, 0x01, 0xFE, 0x18, 0xC0,
    0xF0, 0x0F, 0xFE, 0x00, 0x40, 0x60, 0x20, 0x0C, 0x08, 0x01, 0x02, 0x00,
    0x40, 0x80, 0x10, 0x40, 0x04, 0x10, 0x02, 0x04, 0x01, 0x01, 0x01, 0x80,
    0x7F, 0x80, 0x20, 0x60, 0x08, 0x0C, 0x02, 0x03, 0x80, 0x80, 0x60, 0x40,
    0x18, 0x10, 0x03, 0x04, 0x00, 0xC1, 0x00, 0x1B, 0xF8, 0x07, 0x00, 0x7E,
    0x40, 0x60, 0xF0, 0x20, 0x1C, 0x10, 0x02, 0x08, 0x00, 0x82, 0x00, 0x00,
    0x80, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xC0, 0x00,
    0x18, 0x00, 0x01, 0x00, 0x00, 0x44, 0x00, 0x11, 0x00, 0x04, 0x40, 0x02,
    0x38, 0x01, 0x0B, 0x81, 0x82, 0x3F, 0x80, 0x3F, 0xFF, 0xA0, 0x20, 0x50,
    0x10, 0x28, 0x08, 0x24, 0x08, 0x10, 0x04, 0x00, 0x02, 0x00, 0x01, 0x00,
    0x01, 0x00, 0x00, 0x80, 0x00, 0x40, 0x00, 0x20, 0x00, 0x10, 0x00, 0x10,
    0x00, 0x08, 0x00, 0x04, 0x00, 0x02, 0x00, 0x02, 0x00, 0x01, 0x00, 0x1F,
    0xFC, 0x00, 0x7E, 0x0F, 0xC4, 0x00, 0x42, 0x00, 0x10, 0x80, 0x08, 0x20,
    0x02, 0x08, 0x00, 0x82, 0x00, 0x21, 0x00, 0x08, 0x40, 0x04, 0x10, 0x01,
    0x04, 0x00, 0x41, 0x00, 0x10, 0x80, 0x0C, 0x20, 0x02, 0x08, 0x00, 0x82,
    0x00, 0x60, 0x80, 0x10, 0x10, 0x08, 0x06, 0x0C, 0x00, 0x7C, 0x00, 0xFE,
    0x03, 0xF9, 0x80, 0x02, 0x0C, 0x00, 0x30, 0x20, 0x01, 0x01, 0x00, 0x10,
    0x08, 0x01, 0x80, 0x60, 0x08, 0x03, 0x00, 0xC0, 0x18, 0x04, 0x00, 0x40,
    0x60, 0x02, 0x06, 0x00, 0x10, 0x20, 0x00, 0xC3, 0x00, 0x06, 0x10, 0x00,
    0x31, 0x80, 0x00, 0x88, 0x00, 0x04, 0x80, 0x00, 0x2C, 0x00, 0x01, 0xC0,
    0x00, 0x0E, 0x00, 0x00, 0x7F, 0x07, 0xF2, 0x00, 0x04, 0x20, 0x00, 0xC2,
    0x00, 0x08, 0x20, 0xC0, 0x82, 0x0C, 0x18, 0x21, 0xA1, 0x02, 0x1A, 0x10,
    0x23, 0x23, 0x04, 0x32, 0x30, 0x46, 0x22, 0x04, 0x62, 0x60, 0x4C, 0x26,
    0x04, 0xC2, 0x40, 0x58, 0x24, 0x05, 0x82, 0xC0, 0x70, 0x28, 0x07, 0x02,
    0x80, 0xE0, 0x38, 0x0E, 0x03, 0x00, 0x0F, 0xC1, 0xF8, 0x30, 0x03, 0x00,
    0xC0, 0x30, 0x06, 0x03, 0x00, 0x18, 0x10, 0x00, 0xC1, 0x00, 0x03, 0x18,
    0x00, 0x09, 0x80, 0x00, 0x78, 0x00, 0x01, 0x80, 0x00, 0x1C, 0x00, 0x01,
    0xA0, 0x00, 0x19, 0x80, 0x01, 0x84, 0x00, 0x18, 0x30, 0x01, 0x80, 0xC0,
    0x08, 0x06, 0x00, 0x80, 0x18, 0x08, 0x00, 0xC1, 0xF8, 0x3F, 0x80, 0x7E,
    0x0F, 0xC4, 0x00, 0xC1, 0x80, 0x60, 0x20, 0x30, 0x0C, 0x08, 0x03, 0x04,
    0x00, 0x43, 0x00, 0x19, 0x80, 0x02, 0xC0, 0x00, 0xE0, 0x00, 0x10, 0x00,
    0x04, 0x00, 0x01, 0x00, 0x00, 0x80, 0x00, 0x20, 0x00, 0x08, 0x00, 0x02,
    0x00, 0x01, 0x00, 0x00, 0x40, 0x03, 0xFF, 0x80, 0x0F, 0xFF, 0x86, 0x00,
    0x82, 0x00, 0x81, 0x00, 0xC1, 0x80, 0xC0, 0xC0, 0xC0, 0x00, 0xC0, 0x00,
    0xC0, 0x00, 0x40, 0x00, 0x40, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
    0x60, 0x10, 0x60, 0x18, 0x20, 0x08, 0x20, 0x04, 0x20, 0x02, 0x30, 0x03,
    0x1F, 0xFF, 0x80, 0x07, 0xE0, 0x80, 0x10, 0x02, 0x00, 0xC0, 0x18, 0x02,
    0x00, 0x40, 0x18, 0x03, 0x00, 0x40, 0x08, 0x01, 0x00, 0x60, 0x0C, 0x01,
    0x00, 0x20, 0x04, 0x01, 0x80, 0x30, 0x04, 0x00, 0x80, 0x10, 0x06, 0x00,
    0xFC, 0x00, 0x80, 0x80, 0x80, 0x40, 0x40, 0x40, 0x20, 0x20, 0x20, 0x20,
    0x10, 0x10, 0x10, 0x10, 0x08, 0x08, 0x08, 0x08, 0x04, 0x04, 0x04, 0x04,
    0x02, 0x02, 0x02, 0x02, 0x00, 0x07, 0xE0, 0x0C, 0x01, 0x00, 0x20, 0x04,
    0x01, 0x80, 0x30, 0x04, 0x00, 0x80, 0x30, 0x06, 0x00, 0x80, 0x10, 0x02,
    0x00, 0xC0, 0x18, 0x02, 0x00, 0x40, 0x18, 0x03, 0x00, 0x40, 0x08, 0x03,
    0x00, 0x60, 0xF8, 0x00, 0x01, 0x00, 0x1C, 0x01, 0xB0, 0x19, 0x81, 0x86,
    0x18, 0x11, 0x80, 0xD8, 0x03, 0x80, 0x18, 0xFF, 0xFF, 0xF8, 0xC7, 0x1C,
    0x71, 0x80, 0x03, 0xF8, 0x0C, 0x0C, 0x00, 0x02, 0x00, 0x02, 0x00, 0x02,
    0x00, 0x02, 0x07, 0xFC, 0x18, 0x0C, 0x20, 0x04, 0x40, 0x04, 0x80, 0x04,
    0x80, 0x08, 0x80, 0x38, 0xC0, 0xE8, 0x3F, 0x0F, 0x0F, 0x00, 0x00, 0x20,
    0x00, 0x04, 0x00, 0x01, 0x80, 0x00, 0x30, 0x00, 0x04, 0x00, 0x00, 0x87,
    0xC0, 0x13, 0x0C, 0x06, 0x80, 0x40, 0xE0, 0x0C, 0x18, 0x00, 0x82, 0x00,
    0x10, 0xC0, 0x02, 0x10, 0x00, 0x42, 0x00, 0x08, 0x40, 0x02, 0x08, 0x00,
    0x43, 0x80, 0x10, 0x70, 0x04, 0x09, 0x83, 0x0F, 0x1F, 0x80, 0x01, 0xFC,
    0x83, 0x03, 0xC6, 0x00, 0xE4, 0x00, 0x22, 0x00, 0x12, 0x00, 0x01, 0x00,
    0x01, 0x00, 0x00, 0x80, 0x00, 0x40, 0x00, 0x20, 0x00, 0x18, 0x00, 0x64,
    0x00, 0x61, 0x81, 0xC0, 0x7F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x30, 0x00,
    0x0C, 0x00, 0x02, 0x00, 0x00, 0x80, 0x00, 0x60, 0x3F, 0x18, 0x10, 0x64,
    0x18, 0x0D, 0x08, 0x01, 0xC2, 0x00, 0x71, 0x00, 0x0C, 0x80, 0x02, 0x20,
    0x00, 0x88, 0x00, 0x62, 0x00, 0x18, 0x80, 0x0E, 0x20, 0x03, 0x04, 0x03,
    0x40, 0xC1, 0xB0, 0x1F, 0x8F, 0x00, 0x01, 0xF0, 0x0E, 0x0C, 0x18, 0x06,
    0x30, 0x02, 0x60, 0x01, 0x40, 0x01, 0xC0, 0x01, 0xFF, 0xFF, 0x80, 0x00,
    0x80, 0x00, 0x80, 0x00, 0x40, 0x00, 0x60, 0x06, 0x30, 0x1C, 0x0F, 0xE0,
    0x00, 0x1F, 0xE0, 0x0C, 0x00, 0x03, 0x00, 0x00, 0x40, 0x00, 0x08, 0x00,
    0x02, 0x00, 0x07, 0xFF, 0xC0, 0x08, 0x00, 0x01, 0x00, 0x00, 0x20, 0x00,
    0x08, 0x00, 0x01, 0x00, 0x00, 0x20, 0x00, 0x04, 0x00, 0x00, 0x80, 0x00,
    0x20, 0x00, 0x04, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x04, 0x00, 0x0F,
    0xFF, 0x00, 0x03, 0xE3, 0xE1, 0x83, 0x60, 0x40, 0x38, 0x10, 0x03, 0x04,
    0x00, 0x60, 0x80, 0x0C, 0x20, 0x01, 0x84, 0x00, 0x20, 0x80, 0x04, 0x10,
    0x01, 0x82, 0x00, 0x30, 0x60, 0x0C, 0x04, 0x02, 0x80, 0x61, 0x90, 0x07,
    0xC6, 0x00, 0x00, 0xC0, 0x00, 0x10, 0x00, 0x02, 0x00, 0x00, 0x80, 0x00,
    0x30, 0x00, 0x0C, 0x00, 0xFE, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x40, 0x00,
    0x10, 0x00, 0x08, 0x00, 0x02, 0x00, 0x00, 0x80, 0x00, 0x23, 0xE0, 0x0B,
    0x0C, 0x05, 0x00, 0x81, 0x80, 0x20, 0x40, 0x08, 0x10, 0x02, 0x08, 0x00,
    0x82, 0x00, 0x60, 0x80, 0x18, 0x20, 0x06, 0x10, 0x01, 0x84, 0x00, 0x61,
    0x00, 0x30, 0x40, 0x0C, 0xFC, 0x1F, 0xC0, 0x00, 0x30, 0x00, 0x60, 0x00,
    0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x20,
    0x00, 0x40, 0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00, 0x10, 0x00,
    0x40, 0x00, 0x80, 0x01, 0x00, 0x02, 0x00, 0x08, 0x00, 0x10, 0x1F, 0xFF,
    0x80, 0x00, 0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x60, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x07, 0xFE, 0x00, 0x04, 0x00, 0x08, 0x00, 0x10, 0x00, 0x20,
    0x00, 0x80, 0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00, 0x20, 0x00,
    0x40, 0x00, 0x80, 0x01, 0x00, 0x06, 0x00, 0x08, 0x00, 0x10, 0x00, 0x20,
    0x00, 0x80, 0x03, 0x00, 0x0C, 0x0F, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x60,
    0x00, 0x10, 0x00, 0x04, 0x00, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x30, 0xFC,
    0x08, 0x18, 0x02, 0x0C, 0x00, 0x8C, 0x00, 0x66, 0x00, 0x1B, 0x00, 0x05,
    0x80, 0x01, 0xB0, 0x00, 0x46, 0x00, 0x31, 0xC0, 0x0C, 0x30, 0x02, 0x06,
    0x00, 0x80, 0xC0, 0x60, 0x30, 0xF8, 0x1F, 0x80, 0x01, 0xF8, 0x00, 0x20,
    0x00, 0x40, 0x00, 0x80, 0x01, 0x00, 0x04, 0x00, 0x08, 0x00, 0x10, 0x00,
    0x20, 0x00, 0x80, 0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00, 0x20,
    0x00, 0x40, 0x00, 0x80, 0x01, 0x00, 0x04, 0x00, 0x08, 0x0F, 0xFF, 0xC0,
    0x1C, 0xF1, 0xE0, 0xF1, 0xE3, 0x0E, 0x1C, 0x10, 0xC1, 0x81, 0x08, 0x10,
    0x30, 0x81, 0x03, 0x18, 0x10, 0x21, 0x83, 0x02, 0x10, 0x30, 0x21, 0x02,
    0x06, 0x10, 0x20, 0x63, 0x02, 0x04, 0x30, 0x60, 0x42, 0x06, 0x04, 0xF8,
    0x70, 0xF0, 0x0E, 0x3E, 0x01, 0x60, 0x81, 0xC0, 0x20, 0xC0, 0x10, 0x40,
    0x08, 0x20, 0x04, 0x30, 0x02, 0x10, 0x02, 0x08, 0x01, 0x04, 0x00, 0x82,
    0x00, 0x42, 0x00, 0x21, 0x00, 0x20, 0x80, 0x13, 0xF0, 0x3E, 0x01, 0xF0,
    0x06, 0x0C, 0x18, 0x06, 0x20, 0x03, 0x60, 0x01, 0x40, 0x01, 0x80, 0x01,
    0x80, 0x01, 0x80, 0x01, 0x80, 0x02, 0x80, 0x06, 0xC0, 0x04, 0x40, 0x18,
    0x30, 0x60, 0x1F, 0x80, 0x0F, 0x1F, 0x80, 0x16, 0x0C, 0x01, 0xC0, 0x20,
    0x30, 0x03, 0x03, 0x00, 0x10, 0x20, 0x01, 0x02, 0x00, 0x10, 0x40, 0x01,
    0x04, 0x00, 0x10, 0x40, 0x02, 0x06, 0x00, 0x60, 0x60, 0x04, 0x0B, 0x00,
    0x80, 0x98, 0x30, 0x08, 0xFC, 0x00, 0x80, 0x00, 0x08, 0x00, 0x01, 0x00,
    0x00, 0x10, 0x00, 0x01, 0x00, 0x00, 0x10, 0x00, 0x0F, 0xF0, 0x00, 0x03,
    0xF1, 0xE1, 0x83, 0x20, 0x40, 0x34, 0x10, 0x03, 0x84, 0x00, 0x30, 0x80,
    0x04, 0x20, 0x00, 0x84, 0x00, 0x10, 0x80, 0x06, 0x10, 0x00, 0xC2, 0x00,
    0x30, 0x60, 0x0E, 0x04, 0x03, 0x40, 0x60, 0xC8, 0x07, 0xE2, 0x00, 0x00,
    0x40, 0x00, 0x08, 0x00, 0x01, 0x00, 0x00, 0x20, 0x00, 0x08, 0x00, 0x01,
    0x00, 0x03, 0xFC, 0x00, 0x0F, 0x87, 0xC0, 0x23, 0x08, 0x04, 0xC0, 0x00,
    0xE0, 0x00, 0x18, 0x00, 0x02, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x02,
    0x00, 0x00, 0x40, 0x00, 0x10, 0x00, 0x02, 0x00, 0x00, 0x40, 0x00, 0x08,
    0x00, 0x3F, 0xFE, 0x00, 0x01, 0xFA, 0x0C, 0x1C, 0x20, 0x08, 0x80, 0x11,
    0x00, 0x03, 0x00, 0x03, 0xF8, 0x00, 0x7C, 0x00, 0x0C, 0x00, 0x09, 0x00,
    0x16, 0x00, 0x2C, 0x00, 0x9E, 0x06, 0x27, 0xF0, 0x00, 0x08, 0x00, 0x40,
    0x02, 0x00, 0x10, 0x00, 0x80, 0x7F, 0xFC, 0x40, 0x02, 0x00, 0x10, 0x00,
    0x80, 0x08, 0x00, 0x40, 0x02, 0x00, 0x10, 0x01, 0x00, 0x08, 0x00, 0x40,
    0x02, 0x00, 0xD8, 0x1C, 0x3F, 0x00, 0xF0, 0x1E, 0x20, 0x04, 0x80, 0x09,
    0x00, 0x12, 0x00, 0x24, 0x00, 0xC8, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80,
    0x09, 0x00, 0x12, 0x00, 0x64, 0x03, 0x8C, 0x1D, 0x0F, 0xC3, 0x80, 0xFE,
    0x0F, 0xE6, 0x00, 0x20, 0x40, 0x08, 0x08, 0x03, 0x01, 0x80, 0x40, 0x30,
    0x18, 0x06, 0x02, 0x00, 0x40, 0x80, 0x08, 0x30, 0x01, 0x84, 0x00, 0x31,
    0x80, 0x02, 0x20, 0x00, 0x48, 0x00, 0x09, 0x00, 0x01, 0xC0, 0x00, 0xF8,
    0x0F, 0xA0, 0x01, 0x90, 0x00, 0x88, 0x40, 0xC4, 0x30, 0x42, 0x18, 0x61,
    0x1A, 0x20, 0x8D, 0x10, 0x4C, 0x98, 0x26, 0x48, 0x16, 0x2C, 0x0B, 0x14,
    0x07, 0x0A, 0x03, 0x07, 0x01, 0x81, 0x00, 0x0F, 0x83, 0xE0, 0xC0, 0x18,
    0x0C, 0x0C, 0x01, 0x83, 0x00, 0x18, 0xC0, 0x01, 0xB0, 0x00, 0x1C, 0x00,
    0x03, 0x00, 0x00, 0xF0, 0x00, 0x63, 0x00, 0x18, 0x30, 0x06, 0x06, 0x01,
    0x80, 0x60, 0x60, 0x06, 0x3F, 0x07, 0xE0, 0x0F, 0xC0, 0xF8, 0x30, 0x01,
    0x00, 0x80, 0x18, 0x04, 0x00, 0x80, 0x30, 0x0C, 0x01, 0x80, 0xC0, 0x04,
    0x04, 0x00, 0x30, 0x60, 0x01, 0x86, 0x00, 0x04, 0x20, 0x00, 0x23, 0x00,
    0x01, 0xB0, 0x00, 0x0D, 0x00, 0x00, 0x38, 0x00, 0x01, 0x80, 0x00, 0x08,
    0x00, 0x00, 0xC0, 0x00, 0x04, 0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x00,
    0x20, 0x00, 0x7F, 0xE0, 0x00, 0x1F, 0xFF, 0x10, 0x06, 0x10, 0x0C, 0x10,
    0x18, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00, 0x06,
    0x00, 0x0C, 0x00, 0x18, 0x04, 0x30, 0x0C, 0x60, 0x0C, 0xFF, 0xF8, 0x00,
    0xE0, 0x20, 0x08, 0x01, 0x00, 0x20, 0x04, 0x01, 0x00, 0x20, 0x04, 0x00,
    0x80, 0x20, 0x08, 0x0E, 0x00, 0x60, 0x04, 0x00, 0x80, 0x10, 0x02, 0x00,
    0x40, 0x08, 0x02, 0x00, 0x40, 0x08, 0x01, 0x00, 0x18, 0x00, 0x00, 0x10,
    0xC3, 0x08, 0x20, 0x86, 0x18, 0x41, 0x04, 0x30, 0xC2, 0x08, 0x21, 0x86,
    0x10, 0x43, 0x0C, 0x20, 0x06, 0x00, 0x40, 0x10, 0x04, 0x01, 0x00, 0x40,
    0x10, 0x04, 0x02, 0x00, 0x80, 0x20, 0x0C, 0x01, 0xC0, 0xC0, 0x40, 0x10,
    0x04, 0x03, 0x00, 0x80, 0x20, 0x08, 0x02, 0x01, 0x00, 0xC0, 0xE0, 0x00,
    0x1E, 0x02, 0x66, 0x0D, 0x86, 0x16, 0x06, 0x48, 0x07, 0x00};

const GFXglyph FreeMonoOblique18pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 21, 0, 1},        // 0x20 ' '
    {0, 7, 22, 21, 9, -21},     // 0x21 '!'
    {20, 13, 10, 21, 7, -20},   // 0x22 '"'
    {37, 15, 24, 21, 5, -21},   // 0x23 '#'
    {82, 16, 26, 21, 4, -22},   // 0x24 '$'
    {134, 16, 21, 21, 5, -20},  // 0x25 '%'
    {176, 13, 18, 21, 5, -17},  // 0x26 '&'
    {206, 5, 10, 21, 12, -20},  // 0x27 '''
    {213, 8, 25, 21, 12, -20},  // 0x28 '('
    {238, 8, 25, 21, 5, -20},   // 0x29 ')'
    {263, 14, 11, 21, 7, -19},  // 0x2A '*'
    {283, 15, 17, 21, 5, -17},  // 0x2B '+'
    {315, 9, 10, 21, 4, -4},    // 0x2C ','
    {327, 16, 1, 21, 5, -9},    // 0x2D '-'
    {329, 5, 5, 21, 8, -4},     // 0x2E '.'
    {333, 19, 26, 21, 3, -22},  // 0x2F '/'
    {395, 14, 21, 21, 5, -20},  // 0x30 '0'
    {432, 13, 21, 21, 4, -20},  // 0x31 '1'
    {467, 17, 21, 21, 3, -20},  // 0x32 '2'
    {512, 16, 21, 21, 3, -20},  // 0x33 '3'
    {554, 14, 21, 21, 5, -20},  // 0x34 '4'
    {591, 17, 21, 21, 4, -20},  // 0x35 '5'
    {636, 16, 21, 21, 6, -20},  // 0x36 '6'
    {678, 13, 21, 21, 8, -20},  // 0x37 '7'
    {713, 15, 21, 21, 5, -20},  // 0x38 '8'
    {753, 15, 21, 21, 5, -20},  // 0x39 '9'
    {793, 7, 15, 21, 8, -14},   // 0x3A ':'
    {807, 11, 20, 21, 4, -14},  // 0x3B ';'
    {835, 17, 16, 21, 5, -17},  // 0x3C '<'
    {869, 19, 6, 21, 3, -12},   // 0x3D '='
    {884, 18, 16, 21, 3, -17},  // 0x3E '>'
    {920, 12, 20, 21, 8, -19},  // 0x3F '?'
    {950, 15, 23, 21, 5, -20},  // 0x40 '@'
    {994, 21, 20, 21, 0, -19},  // 0x41 'A'
    {1047, 18, 20, 21, 2, -19}, // 0x42 'B'
    {1092, 18, 20, 21, 4, -19}, // 0x43 'C'
    {1137, 18, 20, 21, 2, -19}, // 0x44 'D'
    {1182, 20, 20, 21, 2, -19}, // 0x45 'E'
    {1232, 20, 20, 21, 2, -19}, // 0x46 'F'
    {1282, 18, 20, 21, 4, -19}, // 0x47 'G'
    {1327, 21, 20, 21, 2, -19}, // 0x48 'H'
    {1380, 17, 20, 21, 4, -19}, // 0x49 'I'
    {1423, 20, 20, 21, 4, -19}, // 0x4A 'J'
    {1473, 21, 20, 21, 2, -19}, // 0x4B 'K'
    {1526, 18, 20, 21, 2, -19}, // 0x4C 'L'
    {1571, 24, 20, 21, 1, -19}, // 0x4D 'M'
    {1631, 22, 20, 21, 2, -19}, // 0x4E 'N'
    {1686, 17, 20, 21, 4, -19}, // 0x4F 'O'
    {1729, 18, 20, 21, 2, -19}, // 0x50 'P'
    {1774, 17, 24, 21, 4, -19}, // 0x51 'Q'
    {1825, 18, 20, 21, 2, -19}, // 0x52 'R'
    {1870, 18, 20, 21, 3, -19}, // 0x53 'S'
    {1915, 17, 20, 21, 5, -19}, // 0x54 'T'
    {1958, 18, 20, 21, 5, -19}, // 0x55 'U'
    {2003, 21, 20, 21, 4, -19}, // 0x56 'V'
    {2056, 20, 20, 21, 4, -19}, // 0x57 'W'
    {2106, 21, 20, 21, 2, -19}, // 0x58 'X'
    {2159, 18, 20, 21, 5, -19}, // 0x59 'Y'
    {2204, 17, 20, 21, 4, -19}, // 0x5A 'Z'
    {2247, 11, 25, 21, 9, -20}, // 0x5B '['
    {2282, 8, 27, 21, 9, -22},  // 0x5C '\'
    {2309, 11, 25, 21, 5, -20}, // 0x5D ']'
    {2344, 13, 9, 21, 7, -20},  // 0x5E '^'
    {2359, 21, 1, 21, -1, 4},   // 0x5F '_'
    {2362, 5, 5, 21, 9, -21},   // 0x60 '`'
    {2366, 16, 15, 21, 3, -14}, // 0x61 'a'
    {2396, 19, 21, 21, 1, -20}, // 0x62 'b'
    {2446, 17, 15, 21, 4, -14}, // 0x63 'c'
    {2478, 18, 21, 21, 4, -20}, // 0x64 'd'
    {2526, 16, 15, 21, 4, -14}, // 0x65 'e'
    {2556, 19, 21, 21, 4, -20}, // 0x66 'f'
    {2606, 19, 22, 21, 4, -14}, // 0x67 'g'
    {2659, 18, 21, 21, 2, -20}, // 0x68 'h'
    {2707, 15, 22, 21, 3, -21}, // 0x69 'i'
    {2749, 15, 29, 21, 3, -21}, // 0x6A 'j'
    {2804, 18, 21, 21, 2, -20}, // 0x6B 'k'
    {2852, 15, 21, 21, 3, -20}, // 0x6C 'l'
    {2892, 20, 15, 21, 1, -14}, // 0x6D 'm'
    {2930, 17, 15, 21, 2, -14}, // 0x6E 'n'
    {2962, 16, 15, 21, 4, -14}, // 0x6F 'o'
    {2992, 20, 22, 21, 0, -14}, // 0x70 'p'
    {3047, 19, 22, 21, 4, -14}, // 0x71 'q'
    {3100, 19, 15, 21, 3, -14}, // 0x72 'r'
    {3136, 15, 15, 21, 4, -14}, // 0x73 's'
    {3165, 13, 20, 21, 5, -19}, // 0x74 't'
    {3198, 15, 15, 21, 4, -14}, // 0x75 'u'
    {3227, 19, 15, 21, 4, -14}, // 0x76 'v'
    {3263, 17, 15, 21, 5, -14}, // 0x77 'w'
    {3295, 19, 15, 21, 2, -14}, // 0x78 'x'
    {3331, 21, 22, 21, 1, -14}, // 0x79 'y'
    {3389, 16, 15, 21, 4, -14}, // 0x7A 'z'
    {3419, 11, 25, 21, 8, -20}, // 0x7B '{'
    {3454, 6, 24, 21, 9, -19},  // 0x7C '|'
    {3472, 10, 25, 21, 6, -20}, // 0x7D '}'
    {3504, 15, 5, 21, 5, -11}}; // 0x7E '~'

const GFXfont FreeMonoOblique18pt7b PROGMEM = {
    (uint8_t *)FreeMonoOblique18pt7bBitmaps,
    (GFXglyph *)FreeMonoOblique18pt7bGlyphs, 0x20, 0x7E, 35};

// Approx. 4186 bytes
