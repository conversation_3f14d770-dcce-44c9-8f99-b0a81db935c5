sudo: false
dist:
  - xenial
addons:
  apt:
    packages:
      - xvfb
language: bash
os:
  - linux
env:
  matrix:
    - CPU="esp8266" BOARD="esp8266com:esp8266:generic:xtal=80" IDE_VERSION=1.6.13
    - CPU="esp8266" BOARD="esp8266com:esp8266:generic:xtal=80,dbg=Serial1" IDE_VERSION=1.6.13
    - CPU="esp8266" BOARD="esp8266com:esp8266:generic:xtal=80,eesz=1M,FlashMode=qio,FlashFreq=80" IDE_VERSION=1.8.13
    - CPU="esp32" BOARD="espressif:esp32:esp32:FlashFreq=80" IDE_VERSION=1.8.5
    - CPU="esp32" BOARD="espressif:esp32:esp32:FlashFreq=80" IDE_VERSION=1.8.9
    - CPU="esp32" BOARD="espressif:esp32:esp32:FlashFreq=80" IDE_VERSION=1.8.13
script:
  - /sbin/start-stop-daemon --start --quiet --pidfile /tmp/custom_xvfb_1.pid --make-pidfile --background --exec /usr/bin/Xvfb -- :1 -ac -screen 0 1280x1024x16
  - export DISPLAY=:1.0
  - sleep 3
  - wget http://downloads.arduino.cc/arduino-$IDE_VERSION-linux64.tar.xz
  - tar xf arduino-$IDE_VERSION-linux64.tar.xz
  - mv arduino-$IDE_VERSION $HOME/arduino_ide
  - export PATH="$HOME/arduino_ide:$PATH"
  - which arduino
  - mkdir -p $HOME/Arduino/libraries

  - wget https://github.com/bblanchon/ArduinoJson/archive/6.x.zip
  - unzip 6.x.zip
  - mv ArduinoJson-6.x $HOME/Arduino/libraries/ArduinoJson
  - cp -r $TRAVIS_BUILD_DIR $HOME/Arduino/libraries/arduinoWebSockets
  - source $TRAVIS_BUILD_DIR/travis/common.sh
  - get_core $CPU
  - cd $TRAVIS_BUILD_DIR
  - arduino --board $BOARD --save-prefs
  - arduino --get-pref sketchbook.path
  - arduino --pref update.check=false
  - build_sketches arduino $HOME/Arduino/libraries/arduinoWebSockets/examples/$CPU $CPU

notifications:
  email:
    on_success: change
    on_failure: change
