name: Compile Arduino WiFiNINA Examples

# See: https://docs.github.com/en/free-pro-team@latest/actions/reference/events-that-trigger-workflows
on:
  push:
    paths:
      - ".github/workflows/compile-arduino_wifinina-examples.yaml"
      - "examples/arduino_wifinina/**"
      - "src/**"
  pull_request:
    paths:
      - ".github/workflows/compile-arduino_wifinina-examples.yaml"
      - "examples/arduino_wifinina/**"
      - "src/**"
  schedule:
    # Run every Tuesday at 8 AM UTC to catch breakage caused by changes to external resources (libraries, platforms).
    - cron: "0 8 * * TUE"
  workflow_dispatch:
  repository_dispatch:

jobs:
  build:
    name: ${{ matrix.board.fqbn }}
    runs-on: ubuntu-latest

    env:
      SKETCHES_REPORTS_PATH: sketches-reports

    strategy:
      fail-fast: false

      matrix:
        board:
          - fqbn: arduino:samd:mkrwifi1010
            platforms: |
              - name: arduino:samd
            artifact-name-suffix: arduino-samd-mkrwifi1010
            libraries: |
              - name: WiFiNINA
          - fqbn: arduino:samd:nano_33_iot
            platforms: |
              - name: arduino:samd
            artifact-name-suffix: arduino-samd-nano_33_iot
            libraries: |
              - name: WiFiNINA
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Compile examples
        uses: arduino/compile-sketches@v1
        with:
          fqbn: ${{ matrix.board.fqbn }}
          platforms: ${{ matrix.board.platforms }}
          libraries: |
            # Install the library from the local path.
            - source-path: ./
            ${{ matrix.board.libraries }}
          sketch-paths: |
            - examples/arduino_wifinina/arduino_wifinina.ino
          enable-deltas-report: true
          sketches-report-path: ${{ env.SKETCHES_REPORTS_PATH }}

      - name: Save sketches report as workflow artifact
        uses: actions/upload-artifact@v4
        with:
          if-no-files-found: error
          path: ${{ env.SKETCHES_REPORTS_PATH }}
          name: sketches-report-${{ matrix.board.artifact-name-suffix }}
