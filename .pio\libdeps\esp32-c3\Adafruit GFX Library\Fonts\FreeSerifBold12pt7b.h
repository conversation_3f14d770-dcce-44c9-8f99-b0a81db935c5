#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifBold12pt7bBitmaps[] PROGMEM = {
    0x7F, 0xFF, 0x77, 0x66, 0x22, 0x00, 0x6F, 0xF7, 0xE3, 0xF1, 0xF8, 0xFC,
    0x7E, 0x3A, 0x09, 0x04, 0x0C, 0x40, 0xCC, 0x0C, 0xC0, 0x8C, 0x18, 0xC7,
    0xFF, 0x18, 0xC1, 0x88, 0x19, 0x81, 0x98, 0xFF, 0xE3, 0x18, 0x31, 0x83,
    0x18, 0x33, 0x03, 0x30, 0x08, 0x01, 0x00, 0xFC, 0x24, 0xEC, 0x8D, 0x90,
    0xBA, 0x07, 0xC0, 0x7E, 0x07, 0xF0, 0x7F, 0x07, 0xF0, 0x9F, 0x11, 0xE2,
    0x3E, 0x46, 0xE9, 0xC7, 0xC0, 0x20, 0x04, 0x00, 0x1E, 0x0C, 0x0E, 0x7F,
    0x07, 0x10, 0x83, 0xC4, 0x40, 0xE1, 0x30, 0x38, 0x88, 0x0E, 0x26, 0x03,
    0x91, 0x1E, 0x78, 0x8E, 0x40, 0x27, 0x10, 0x11, 0xC4, 0x0C, 0xE1, 0x02,
    0x38, 0x81, 0x0E, 0x20, 0x43, 0x90, 0x20, 0x78, 0x03, 0xE0, 0x01, 0x9E,
    0x00, 0xE3, 0x80, 0x38, 0xE0, 0x0F, 0x30, 0x03, 0xF0, 0x00, 0x78, 0x7C,
    0x1F, 0x06, 0x1B, 0xE1, 0x1C, 0x7C, 0x8F, 0x1F, 0x23, 0xC3, 0xF0, 0xF8,
    0x7C, 0x3E, 0x0F, 0x97, 0xC7, 0xFC, 0xFE, 0x3E, 0xFF, 0xFE, 0x90, 0x00,
    0x31, 0x0C, 0x31, 0x86, 0x38, 0xE3, 0x8E, 0x38, 0xE3, 0x86, 0x18, 0x60,
    0xC1, 0x02, 0x04, 0x03, 0x06, 0x0C, 0x30, 0x61, 0x87, 0x1C, 0x71, 0xC7,
    0x1C, 0x71, 0x86, 0x38, 0xC2, 0x10, 0x80, 0x1C, 0x6E, 0xFA, 0xEF, 0xF1,
    0xC7, 0xFF, 0xAF, 0xBB, 0x1C, 0x04, 0x00, 0x06, 0x00, 0x60, 0x06, 0x00,
    0x60, 0x06, 0x0F, 0xFF, 0xFF, 0xF0, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
    0x60, 0x6F, 0xF7, 0x11, 0x24, 0xFF, 0xFF, 0xC0, 0x6F, 0xF6, 0x03, 0x07,
    0x06, 0x06, 0x0C, 0x0C, 0x0C, 0x18, 0x18, 0x18, 0x30, 0x30, 0x30, 0x60,
    0x60, 0x60, 0xC0, 0x0E, 0x07, 0x71, 0xC7, 0x38, 0xEF, 0x1D, 0xE3, 0xFC,
    0x7F, 0x8F, 0xF1, 0xFE, 0x3F, 0xC7, 0xF8, 0xF7, 0x1C, 0xE3, 0x8E, 0xE0,
    0xF8, 0x06, 0x0F, 0x1F, 0x83, 0xC1, 0xE0, 0xF0, 0x78, 0x3C, 0x1E, 0x0F,
    0x07, 0x83, 0xC1, 0xE0, 0xF0, 0xF9, 0xFF, 0x0F, 0x03, 0xFC, 0x7F, 0xC4,
    0x3E, 0x01, 0xE0, 0x1E, 0x01, 0xE0, 0x1C, 0x03, 0x80, 0x30, 0x06, 0x00,
    0xC1, 0x18, 0x13, 0xFE, 0x7F, 0xEF, 0xFE, 0x1F, 0x0C, 0xFA, 0x0F, 0x01,
    0xE0, 0x38, 0x0E, 0x03, 0xE0, 0x3E, 0x03, 0xE0, 0x3C, 0x03, 0x80, 0x70,
    0x0D, 0xC1, 0xBC, 0x43, 0xF0, 0x03, 0x80, 0xE0, 0x78, 0x3E, 0x17, 0x89,
    0xE2, 0x79, 0x1E, 0x87, 0xA1, 0xEF, 0xFF, 0xFF, 0xFF, 0xC1, 0xE0, 0x78,
    0x1E, 0x3F, 0xE7, 0xF8, 0xFF, 0x10, 0x04, 0x00, 0xF8, 0x1F, 0xC7, 0xFC,
    0x1F, 0xC0, 0x78, 0x07, 0x00, 0x60, 0x0D, 0xC1, 0x3C, 0x43, 0xF0, 0x00,
    0xE0, 0xF0, 0x38, 0x1E, 0x07, 0x80, 0xF0, 0x3F, 0xE7, 0x9E, 0xF1, 0xFE,
    0x3F, 0xC7, 0xF8, 0xF7, 0x1E, 0xE3, 0x8E, 0x60, 0xF8, 0x7F, 0xEF, 0xFD,
    0xFF, 0xA0, 0x68, 0x0C, 0x03, 0x80, 0x60, 0x0C, 0x03, 0x00, 0x60, 0x0C,
    0x03, 0x00, 0x60, 0x1C, 0x03, 0x00, 0x60, 0x1F, 0x0E, 0x73, 0x87, 0x70,
    0xEF, 0x1D, 0xF3, 0x1F, 0x81, 0xF8, 0x1F, 0xCC, 0xFB, 0x8F, 0xF0, 0xFE,
    0x1F, 0xC3, 0x9C, 0xF1, 0xF8, 0x1F, 0x06, 0x71, 0xC7, 0x78, 0xEF, 0x1F,
    0xE3, 0xFC, 0x7F, 0x8F, 0x79, 0xE7, 0xFC, 0x0F, 0x01, 0xC0, 0x78, 0x1C,
    0x0F, 0x07, 0x00, 0x6F, 0xF6, 0x00, 0x06, 0xFF, 0x60, 0x6F, 0xF6, 0x00,
    0x06, 0xFF, 0x71, 0x22, 0xC0, 0x00, 0x04, 0x00, 0x70, 0x07, 0xC0, 0xFC,
    0x0F, 0x80, 0xF8, 0x0F, 0x80, 0x1F, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0x1F,
    0x00, 0x1F, 0x00, 0x1C, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x03, 0x80, 0x0F,
    0x80, 0x0F, 0x80, 0x0F, 0x80, 0x0F, 0x80, 0x0F, 0x80, 0x1F, 0x01, 0xF0,
    0x1F, 0x03, 0xF0, 0x3E, 0x00, 0xE0, 0x02, 0x00, 0x00, 0x3E, 0x11, 0xEC,
    0x3F, 0x8F, 0xE3, 0xC0, 0xF0, 0x78, 0x18, 0x08, 0x02, 0x00, 0x00, 0x00,
    0x1C, 0x07, 0x81, 0xE0, 0x30, 0x03, 0xF0, 0x0E, 0x18, 0x18, 0x04, 0x30,
    0x66, 0x70, 0xDB, 0x61, 0x99, 0xE3, 0x19, 0xE3, 0x31, 0xE6, 0x31, 0xE6,
    0x31, 0xE6, 0xF2, 0x66, 0xB2, 0x73, 0x3C, 0x38, 0x00, 0x1E, 0x04, 0x03,
    0xF8, 0x00, 0x80, 0x00, 0xC0, 0x00, 0x70, 0x00, 0x38, 0x00, 0x3E, 0x00,
    0x1F, 0x00, 0x1B, 0xC0, 0x09, 0xE0, 0x0C, 0xF8, 0x04, 0x3C, 0x02, 0x1F,
    0x03, 0xFF, 0x81, 0x03, 0xC1, 0x80, 0xF0, 0x80, 0x7D, 0xF0, 0xFF, 0xFF,
    0xC0, 0xF3, 0xC3, 0xC7, 0x8F, 0x1E, 0x3C, 0x78, 0xF1, 0xE3, 0xCE, 0x0F,
    0xF0, 0x3C, 0x70, 0xF0, 0xE3, 0xC3, 0xCF, 0x0F, 0x3C, 0x3C, 0xF0, 0xE3,
    0xC7, 0xBF, 0xF8, 0x07, 0xE2, 0x38, 0x7C, 0xE0, 0x3B, 0xC0, 0x37, 0x00,
    0x7E, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x03,
    0x80, 0x07, 0x80, 0x27, 0x00, 0xC7, 0x86, 0x03, 0xF0, 0xFF, 0xE0, 0x1E,
    0x1E, 0x0F, 0x07, 0x87, 0x81, 0xE3, 0xC0, 0xF1, 0xE0, 0x3C, 0xF0, 0x1E,
    0x78, 0x0F, 0x3C, 0x07, 0x9E, 0x03, 0xCF, 0x01, 0xE7, 0x80, 0xE3, 0xC0,
    0xF1, 0xE0, 0xF0, 0xF0, 0xE1, 0xFF, 0xC0, 0xFF, 0xFC, 0x78, 0x38, 0xF0,
    0x31, 0xE0, 0x23, 0xC4, 0x07, 0x88, 0x0F, 0x30, 0x1F, 0xE0, 0x3C, 0xC0,
    0x78, 0x80, 0xF1, 0x01, 0xE0, 0x23, 0xC0, 0x47, 0x81, 0x8F, 0x07, 0x7F,
    0xFE, 0xFF, 0xFC, 0xF0, 0x73, 0xC0, 0xCF, 0x01, 0x3C, 0x40, 0xF1, 0x03,
    0xCC, 0x0F, 0xF0, 0x3C, 0xC0, 0xF1, 0x03, 0xC4, 0x0F, 0x00, 0x3C, 0x00,
    0xF0, 0x03, 0xC0, 0x3F, 0xC0, 0x07, 0xE2, 0x1C, 0x3E, 0x38, 0x0E, 0x78,
    0x06, 0x70, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xF0,
    0x7F, 0xF0, 0x1E, 0x70, 0x1E, 0x78, 0x1E, 0x38, 0x1E, 0x1E, 0x1E, 0x07,
    0xF0, 0xFE, 0xFF, 0x78, 0x3C, 0x78, 0x3C, 0x78, 0x3C, 0x78, 0x3C, 0x78,
    0x3C, 0x78, 0x3C, 0x7F, 0xFC, 0x78, 0x3C, 0x78, 0x3C, 0x78, 0x3C, 0x78,
    0x3C, 0x78, 0x3C, 0x78, 0x3C, 0x78, 0x3C, 0xFE, 0xFF, 0xFF, 0x3C, 0x3C,
    0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C,
    0xFF, 0x0F, 0xF0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0,
    0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0xE3, 0xCE,
    0x38, 0xE3, 0x83, 0xE0, 0xFE, 0x7F, 0x3C, 0x0E, 0x1E, 0x04, 0x0F, 0x04,
    0x07, 0x84, 0x03, 0xCC, 0x01, 0xEE, 0x00, 0xFF, 0x00, 0x7F, 0xC0, 0x3C,
    0xF0, 0x1E, 0x7C, 0x0F, 0x1F, 0x07, 0x87, 0xC3, 0xC1, 0xF1, 0xE0, 0x7D,
    0xFC, 0xFF, 0xFE, 0x01, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x78, 0x01, 0xE0,
    0x07, 0x80, 0x1E, 0x00, 0x78, 0x01, 0xE0, 0x07, 0x80, 0x1E, 0x01, 0x78,
    0x0D, 0xE0, 0x67, 0x83, 0xBF, 0xFE, 0xFC, 0x01, 0xF3, 0xC0, 0x3E, 0x3E,
    0x03, 0xE2, 0xE0, 0x5E, 0x2F, 0x05, 0xE2, 0xF0, 0x5E, 0x27, 0x09, 0xE2,
    0x78, 0x9E, 0x23, 0x91, 0xE2, 0x3D, 0x1E, 0x23, 0xF1, 0xE2, 0x1E, 0x1E,
    0x21, 0xE1, 0xE2, 0x0C, 0x1E, 0x20, 0xC1, 0xEF, 0x88, 0x3F, 0xF8, 0x1E,
    0xF8, 0x18, 0xF8, 0x11, 0xF8, 0x22, 0xF8, 0x45, 0xF0, 0x89, 0xF1, 0x11,
    0xF2, 0x21, 0xF4, 0x41, 0xF8, 0x81, 0xF1, 0x01, 0xE2, 0x03, 0xC4, 0x03,
    0x8C, 0x03, 0x7C, 0x02, 0x07, 0xF0, 0x0F, 0x1E, 0x0E, 0x03, 0x8F, 0x01,
    0xE7, 0x00, 0x77, 0x80, 0x3F, 0xC0, 0x1F, 0xE0, 0x0F, 0xF0, 0x07, 0xF8,
    0x03, 0xFC, 0x01, 0xEE, 0x00, 0xE7, 0x80, 0xF1, 0xC0, 0x70, 0x70, 0x70,
    0x0F, 0xE0, 0xFF, 0x87, 0x9E, 0x78, 0xF7, 0x8F, 0x78, 0xF7, 0x8F, 0x78,
    0xF7, 0x9E, 0x7F, 0x87, 0x80, 0x78, 0x07, 0x80, 0x78, 0x07, 0x80, 0x78,
    0x0F, 0xE0, 0x07, 0xF0, 0x0F, 0x1E, 0x0E, 0x07, 0x8F, 0x01, 0xE7, 0x00,
    0xF7, 0x80, 0x3F, 0xC0, 0x1F, 0xE0, 0x0F, 0xF0, 0x07, 0xF8, 0x03, 0xFC,
    0x01, 0xEE, 0x00, 0xE7, 0x00, 0xF1, 0xC0, 0x70, 0x70, 0x70, 0x1C, 0xF0,
    0x03, 0xE0, 0x01, 0xF8, 0x00, 0x3E, 0x00, 0x07, 0xE0, 0xFF, 0xE0, 0x3C,
    0x78, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x3C, 0x38, 0x3C,
    0x70, 0x3F, 0xC0, 0x3D, 0xE0, 0x3C, 0xF0, 0x3C, 0xF8, 0x3C, 0x78, 0x3C,
    0x3C, 0x3C, 0x3E, 0xFF, 0x1F, 0x1F, 0x27, 0x0E, 0x60, 0x6E, 0x06, 0xF0,
    0x2F, 0x80, 0x7F, 0x07, 0xFC, 0x1F, 0xE0, 0x7E, 0x01, 0xF8, 0x07, 0xC0,
    0x7C, 0x06, 0xF0, 0xC9, 0xF8, 0xFF, 0xFF, 0xC7, 0x9F, 0x0F, 0x1C, 0x1E,
    0x10, 0x3C, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0x80,
    0x0F, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xF0, 0x07, 0xF8, 0xFE,
    0x1E, 0xF0, 0x09, 0xE0, 0x13, 0xC0, 0x27, 0x80, 0x4F, 0x00, 0x9E, 0x01,
    0x3C, 0x02, 0x78, 0x04, 0xF0, 0x09, 0xE0, 0x13, 0xC0, 0x27, 0x80, 0x47,
    0x81, 0x07, 0x84, 0x07, 0xF0, 0xFF, 0x0F, 0x9E, 0x03, 0x0F, 0x00, 0x83,
    0xC0, 0x81, 0xE0, 0x40, 0xF8, 0x60, 0x3C, 0x20, 0x1E, 0x10, 0x07, 0x90,
    0x03, 0xC8, 0x00, 0xF4, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x0E, 0x00, 0x07,
    0x00, 0x01, 0x80, 0x00, 0x80, 0x00, 0xFE, 0x7F, 0x9E, 0xF8, 0x3C, 0x08,
    0xF0, 0x78, 0x31, 0xE0, 0xF0, 0x41, 0xE0, 0xF0, 0x83, 0xC3, 0xE3, 0x07,
    0x85, 0xC4, 0x07, 0x93, 0xC8, 0x0F, 0x27, 0xB0, 0x0E, 0x47, 0x40, 0x1F,
    0x0F, 0x80, 0x3E, 0x1F, 0x00, 0x38, 0x1C, 0x00, 0x70, 0x38, 0x00, 0xE0,
    0x30, 0x00, 0x80, 0x40, 0xFF, 0x9F, 0x9F, 0x07, 0x07, 0x83, 0x03, 0xE3,
    0x00, 0xF9, 0x00, 0x3D, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xE0, 0x00,
    0xF8, 0x00, 0xBE, 0x00, 0x8F, 0x00, 0x83, 0xC0, 0xC1, 0xF0, 0xE0, 0xFD,
    0xF8, 0xFF, 0xFF, 0x1F, 0x7C, 0x06, 0x3C, 0x04, 0x3E, 0x0C, 0x1E, 0x08,
    0x0F, 0x10, 0x0F, 0x30, 0x07, 0xA0, 0x07, 0xC0, 0x03, 0xC0, 0x03, 0xC0,
    0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x0F, 0xF0, 0x7F, 0xFC,
    0xE0, 0xF1, 0x83, 0xE2, 0x07, 0x84, 0x1E, 0x00, 0x7C, 0x00, 0xF0, 0x03,
    0xC0, 0x0F, 0x80, 0x1E, 0x00, 0x7C, 0x08, 0xF0, 0x13, 0xC0, 0x6F, 0x81,
    0x9E, 0x07, 0x7F, 0xFE, 0xFF, 0x39, 0xCE, 0x73, 0x9C, 0xE7, 0x39, 0xCE,
    0x73, 0x9C, 0xE7, 0x39, 0xF0, 0xC0, 0x60, 0x60, 0x60, 0x30, 0x30, 0x30,
    0x18, 0x18, 0x18, 0x0C, 0x0C, 0x0C, 0x06, 0x06, 0x06, 0x03, 0xF9, 0xCE,
    0x73, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x9C, 0xE7, 0x39, 0xCF, 0xF0, 0x0C,
    0x07, 0x81, 0xE0, 0xCC, 0x33, 0x18, 0x66, 0x1B, 0x87, 0xC0, 0xC0, 0xFF,
    0xF0, 0xC7, 0x1C, 0x30, 0x1F, 0x0E, 0x71, 0xCF, 0x39, 0xE0, 0x3C, 0x1F,
    0x8E, 0xF3, 0x9E, 0xF3, 0xDE, 0x79, 0xFF, 0x80, 0xF8, 0x07, 0x80, 0x78,
    0x07, 0x80, 0x78, 0x07, 0xB8, 0x7D, 0xE7, 0x8E, 0x78, 0xF7, 0x8F, 0x78,
    0xF7, 0x8F, 0x78, 0xF7, 0x8E, 0x79, 0xC4, 0x78, 0x1F, 0x1D, 0xDC, 0xFE,
    0x7F, 0x07, 0x83, 0xC1, 0xE0, 0x78, 0x3C, 0x47, 0xC0, 0x03, 0xE0, 0x1E,
    0x01, 0xE0, 0x1E, 0x01, 0xE1, 0xDE, 0x7B, 0xE7, 0x1E, 0xF1, 0xEF, 0x1E,
    0xF1, 0xEF, 0x1E, 0xF1, 0xE7, 0x1E, 0x7B, 0xE1, 0xDF, 0x1F, 0x0C, 0x67,
    0x1B, 0xC7, 0xFF, 0xFC, 0x0F, 0x03, 0xC0, 0x78, 0x4E, 0x21, 0xF0, 0x1E,
    0x3B, 0x7B, 0x78, 0x78, 0xFC, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
    0x78, 0x78, 0xFC, 0x3E, 0x0E, 0x7F, 0xCE, 0x79, 0xEF, 0x3C, 0xE7, 0x0F,
    0xC1, 0x00, 0x60, 0x1C, 0x03, 0xFE, 0x7F, 0xE3, 0xFF, 0x80, 0xF0, 0x33,
    0xFC, 0xF8, 0x07, 0x80, 0x78, 0x07, 0x80, 0x78, 0x07, 0xB8, 0x7D, 0xE7,
    0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xEF,
    0xFF, 0x31, 0xE7, 0x8C, 0x03, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7,
    0xBF, 0x06, 0x0F, 0x0F, 0x06, 0x00, 0x1F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F,
    0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0xCF, 0xCE, 0x7C, 0xF8, 0x03,
    0xC0, 0x1E, 0x00, 0xF0, 0x07, 0x80, 0x3C, 0xF9, 0xE1, 0x8F, 0x10, 0x79,
    0x03, 0xD8, 0x1F, 0xE0, 0xF7, 0x87, 0x9E, 0x3C, 0x71, 0xE3, 0xDF, 0xBF,
    0xF9, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0xBF,
    0xFB, 0xCF, 0x0F, 0xBE, 0x79, 0xE7, 0x8F, 0x3C, 0xF1, 0xE7, 0x9E, 0x3C,
    0xF3, 0xC7, 0x9E, 0x78, 0xF3, 0xCF, 0x1E, 0x79, 0xE3, 0xCF, 0x3C, 0x7B,
    0xFF, 0xDF, 0x80, 0xFB, 0x87, 0xDE, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E,
    0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0xFF, 0xF0, 0x1F, 0x07, 0x71, 0xC7,
    0x78, 0xFF, 0x1F, 0xE3, 0xFC, 0x7F, 0x8F, 0x71, 0xC7, 0x70, 0x7C, 0x00,
    0xFB, 0x87, 0xDE, 0x78, 0xE7, 0x8F, 0x78, 0xF7, 0x8F, 0x78, 0xF7, 0x8F,
    0x78, 0xE7, 0x9E, 0x7F, 0x87, 0x80, 0x78, 0x07, 0x80, 0x78, 0x0F, 0xC0,
    0x1E, 0x23, 0x9E, 0x71, 0xEF, 0x1E, 0xF1, 0xEF, 0x1E, 0xF1, 0xEF, 0x1E,
    0x71, 0xE7, 0x9E, 0x1F, 0xE0, 0x1E, 0x01, 0xE0, 0x1E, 0x01, 0xE0, 0x3F,
    0xF9, 0xDF, 0xF7, 0xDD, 0xE0, 0x78, 0x1E, 0x07, 0x81, 0xE0, 0x78, 0x1E,
    0x0F, 0xC0, 0x3D, 0x43, 0xC3, 0xE0, 0xFC, 0x7E, 0x1F, 0x87, 0x83, 0xC2,
    0xBC, 0x08, 0x18, 0x38, 0x78, 0xFC, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
    0x78, 0x78, 0x79, 0x3E, 0xFB, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7,
    0x9E, 0x79, 0xE7, 0x9E, 0x79, 0xE7, 0x9E, 0x3F, 0xF0, 0xFC, 0xEF, 0x08,
    0xE1, 0x1E, 0x41, 0xC8, 0x3D, 0x03, 0xC0, 0x78, 0x0E, 0x00, 0xC0, 0x10,
    0x00, 0xFD, 0xF7, 0xBC, 0x71, 0x9E, 0x38, 0x87, 0x1E, 0x43, 0xCF, 0x40,
    0xEB, 0xA0, 0x7C, 0xF0, 0x1C, 0x70, 0x0E, 0x38, 0x06, 0x08, 0x01, 0x04,
    0x00, 0xFC, 0xF7, 0x84, 0x3C, 0x81, 0xF0, 0x0F, 0x00, 0xF0, 0x0F, 0x80,
    0xBC, 0x13, 0xC2, 0x1E, 0xFB, 0xF0, 0xFC, 0xEF, 0x08, 0xE1, 0x1E, 0x43,
    0xC8, 0x3A, 0x07, 0xC0, 0x78, 0x0E, 0x01, 0xC0, 0x18, 0x02, 0x00, 0x41,
    0xC8, 0x3A, 0x03, 0x80, 0xFF, 0xB1, 0xE8, 0x70, 0x3C, 0x1E, 0x07, 0x83,
    0xC1, 0xE0, 0x78, 0xBC, 0x2F, 0xF8, 0x07, 0x0E, 0x1C, 0x1C, 0x1C, 0x1C,
    0x1C, 0x1C, 0x1C, 0x1C, 0xE0, 0x18, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
    0x1C, 0x1E, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xE0, 0x70, 0x38, 0x38,
    0x38, 0x38, 0x38, 0x38, 0x38, 0x18, 0x07, 0x38, 0x38, 0x38, 0x38, 0x38,
    0x38, 0x38, 0x38, 0x70, 0xE0, 0x70, 0x1F, 0x8B, 0x3F, 0x01, 0xC0};

const GFXglyph FreeSerifBold12pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 6, 0, 1},         // 0x20 ' '
    {0, 4, 16, 8, 2, -15},      // 0x21 '!'
    {8, 9, 7, 13, 2, -15},      // 0x22 '"'
    {16, 12, 16, 12, 0, -15},   // 0x23 '#'
    {40, 11, 20, 12, 1, -17},   // 0x24 '$'
    {68, 18, 16, 24, 3, -15},   // 0x25 '%'
    {104, 18, 16, 20, 1, -15},  // 0x26 '&'
    {140, 3, 7, 7, 2, -15},     // 0x27 '''
    {143, 6, 21, 8, 1, -16},    // 0x28 '('
    {159, 6, 21, 8, 1, -16},    // 0x29 ')'
    {175, 9, 10, 12, 2, -15},   // 0x2A '*'
    {187, 12, 12, 16, 2, -11},  // 0x2B '+'
    {205, 4, 8, 6, 1, -3},      // 0x2C ','
    {209, 6, 3, 8, 1, -6},      // 0x2D '-'
    {212, 4, 4, 6, 1, -3},      // 0x2E '.'
    {214, 8, 17, 7, -1, -15},   // 0x2F '/'
    {231, 11, 16, 12, 1, -15},  // 0x30 '0'
    {253, 9, 16, 12, 1, -15},   // 0x31 '1'
    {271, 12, 16, 12, 0, -15},  // 0x32 '2'
    {295, 11, 16, 12, 1, -15},  // 0x33 '3'
    {317, 10, 16, 12, 1, -15},  // 0x34 '4'
    {337, 11, 16, 12, 1, -15},  // 0x35 '5'
    {359, 11, 16, 12, 1, -15},  // 0x36 '6'
    {381, 11, 16, 12, 0, -15},  // 0x37 '7'
    {403, 11, 16, 12, 1, -15},  // 0x38 '8'
    {425, 11, 16, 12, 1, -15},  // 0x39 '9'
    {447, 4, 11, 8, 2, -10},    // 0x3A ':'
    {453, 4, 15, 8, 2, -10},    // 0x3B ';'
    {461, 14, 14, 16, 1, -12},  // 0x3C '<'
    {486, 14, 8, 16, 1, -9},    // 0x3D '='
    {500, 14, 14, 16, 1, -12},  // 0x3E '>'
    {525, 10, 16, 12, 1, -15},  // 0x3F '?'
    {545, 16, 16, 22, 3, -15},  // 0x40 '@'
    {577, 17, 16, 17, 0, -15},  // 0x41 'A'
    {611, 14, 16, 16, 1, -15},  // 0x42 'B'
    {639, 15, 16, 17, 1, -15},  // 0x43 'C'
    {669, 17, 16, 18, 0, -15},  // 0x44 'D'
    {703, 15, 16, 16, 1, -15},  // 0x45 'E'
    {733, 14, 16, 15, 1, -15},  // 0x46 'F'
    {761, 16, 16, 19, 1, -15},  // 0x47 'G'
    {793, 16, 16, 19, 2, -15},  // 0x48 'H'
    {825, 8, 16, 9, 1, -15},    // 0x49 'I'
    {841, 12, 18, 12, 0, -15},  // 0x4A 'J'
    {868, 17, 16, 19, 2, -15},  // 0x4B 'K'
    {902, 14, 16, 16, 2, -15},  // 0x4C 'L'
    {930, 20, 16, 23, 1, -15},  // 0x4D 'M'
    {970, 15, 16, 17, 1, -15},  // 0x4E 'N'
    {1000, 17, 16, 19, 1, -15}, // 0x4F 'O'
    {1034, 12, 16, 15, 2, -15}, // 0x50 'P'
    {1058, 17, 20, 19, 1, -15}, // 0x51 'Q'
    {1101, 16, 16, 17, 1, -15}, // 0x52 'R'
    {1133, 12, 16, 14, 1, -15}, // 0x53 'S'
    {1157, 15, 16, 15, 0, -15}, // 0x54 'T'
    {1187, 15, 16, 17, 1, -15}, // 0x55 'U'
    {1217, 17, 17, 17, 0, -15}, // 0x56 'V'
    {1254, 23, 16, 24, 0, -15}, // 0x57 'W'
    {1300, 17, 16, 17, 0, -15}, // 0x58 'X'
    {1334, 16, 16, 17, 1, -15}, // 0x59 'Y'
    {1366, 15, 16, 16, 0, -15}, // 0x5A 'Z'
    {1396, 5, 20, 8, 2, -15},   // 0x5B '['
    {1409, 8, 17, 7, -1, -15},  // 0x5C '\'
    {1426, 5, 20, 8, 2, -15},   // 0x5D ']'
    {1439, 10, 9, 14, 2, -15},  // 0x5E '^'
    {1451, 12, 1, 12, 0, 4},    // 0x5F '_'
    {1453, 5, 4, 8, 0, -16},    // 0x60 '`'
    {1456, 11, 11, 12, 1, -10}, // 0x61 'a'
    {1472, 12, 16, 13, 1, -15}, // 0x62 'b'
    {1496, 9, 11, 10, 1, -10},  // 0x63 'c'
    {1509, 12, 16, 13, 1, -15}, // 0x64 'd'
    {1533, 10, 11, 11, 1, -10}, // 0x65 'e'
    {1547, 8, 16, 9, 1, -15},   // 0x66 'f'
    {1563, 11, 16, 12, 1, -10}, // 0x67 'g'
    {1585, 12, 16, 13, 1, -15}, // 0x68 'h'
    {1609, 6, 16, 7, 1, -15},   // 0x69 'i'
    {1621, 8, 21, 10, 0, -15},  // 0x6A 'j'
    {1642, 13, 16, 13, 1, -15}, // 0x6B 'k'
    {1668, 6, 16, 7, 1, -15},   // 0x6C 'l'
    {1680, 19, 11, 20, 1, -10}, // 0x6D 'm'
    {1707, 12, 11, 13, 1, -10}, // 0x6E 'n'
    {1724, 11, 11, 12, 1, -10}, // 0x6F 'o'
    {1740, 12, 16, 13, 1, -10}, // 0x70 'p'
    {1764, 12, 16, 13, 1, -10}, // 0x71 'q'
    {1788, 10, 11, 10, 1, -10}, // 0x72 'r'
    {1802, 8, 11, 10, 1, -10},  // 0x73 's'
    {1813, 8, 15, 8, 1, -14},   // 0x74 't'
    {1828, 12, 11, 14, 1, -10}, // 0x75 'u'
    {1845, 11, 11, 12, 0, -10}, // 0x76 'v'
    {1861, 17, 11, 17, 0, -10}, // 0x77 'w'
    {1885, 12, 11, 12, 0, -10}, // 0x78 'x'
    {1902, 11, 16, 12, 0, -10}, // 0x79 'y'
    {1924, 10, 11, 11, 1, -10}, // 0x7A 'z'
    {1938, 8, 21, 9, 0, -16},   // 0x7B '{'
    {1959, 2, 17, 5, 2, -15},   // 0x7C '|'
    {1964, 8, 21, 9, 2, -16},   // 0x7D '}'
    {1985, 11, 4, 12, 1, -7}};  // 0x7E '~'

const GFXfont FreeSerifBold12pt7b PROGMEM = {
    (uint8_t *)FreeSerifBold12pt7bBitmaps,
    (GFXglyph *)FreeSerifBold12pt7bGlyphs, 0x20, 0x7E, 29};

// Approx. 2663 bytes
