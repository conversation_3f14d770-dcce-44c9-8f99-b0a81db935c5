/**
 * @file WebSocketsVersion.h
 * @date 13.08.2025
 * <AUTHOR>
 *
 * Copyright (c) 2015 <PERSON>. All rights reserved.
 * This file is part of the WebSockets for Arduino.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 *
 */

#ifndef WEBSOCKETSVERSION_H_
#define WEBSOCKETSVERSION_H_

#define WEBSOCKETS_VERSION "2.7.0"

#define WEBSOCKETS_VERSION_MAJOR 2
#define WEBSOCKETS_VERSION_MINOR 7
#define WEBSOCKETS_VERSION_PATCH 0

#define WEBSOCKETS_VERSION_INT 2007000

#endif /* WEBSOCKETSVERSION_H_ */
